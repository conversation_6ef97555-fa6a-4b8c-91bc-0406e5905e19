/**
 * BookInn Management System - Unified CSS
 * 
 * This file consolidates all management-related styles into a single,
 * conflict-free stylesheet that replaces the fragmented approach.
 * 
 * @version 2.0.0
 * <AUTHOR> Team
 */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
    /* Simplified Color Scheme - Only Blue, White, and Green */
    --bookinn-primary: #1e40af;        /* Header Blue */
    --bookinn-primary-dark: #1e3a8a;
    --bookinn-primary-light: #3b82f6;
    --bookinn-primary-hover: #1d4ed8;

    /* Limited Colors */
    --bookinn-secondary: #64748b;      /* Neutral gray for secondary elements */
    --bookinn-success: #16a34a;        /* Green for New buttons only */
    --bookinn-warning: #64748b;        /* Use gray instead of orange */
    --bookinn-danger: #64748b;         /* Use gray instead of red */
    --bookinn-info: #1e40af;           /* Use primary blue */
    
    /* Background Colors */
    --bookinn-bg-primary: #ffffff;
    --bookinn-bg-secondary: #f8fafc;
    --bookinn-bg-tertiary: #f1f5f9;
    
    /* Text Colors */
    --bookinn-text-primary: #1a202c;
    --bookinn-text-secondary: #4a5568;
    --bookinn-text-muted: #718096;
    --bookinn-text-white: #ffffff;
    
    /* Gray Palette - Enhanced from frontend-dashboard */
    --bookinn-gray-50: #f8fafc;
    --bookinn-gray-100: #f1f5f9;
    --bookinn-gray-200: #e2e8f0;
    --bookinn-gray-300: #cbd5e1;
    --bookinn-gray-400: #94a3b8;
    --bookinn-gray-500: #64748b;
    --bookinn-gray-600: #475569;
    --bookinn-gray-700: #334155;
    --bookinn-gray-800: #1e293b;
    --bookinn-gray-900: #0f172a;

    /* Border Colors */
    --bookinn-border-light: #e2e8f0;
    --bookinn-border-medium: #cbd5e0;
    --bookinn-border-dark: #a0aec0;
    
    /* Design System Tokens - Enhanced from frontend-dashboard */
    --bookinn-border-radius: 6px;
    --bookinn-border-radius-lg: 8px;
    --bookinn-border-radius-xl: 12px;

    /* Shadows */
    --bookinn-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.03);
    --bookinn-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06), 0 1px 2px -1px rgb(0 0 0 / 0.06);
    --bookinn-shadow-lg: 0 4px 6px -1px rgb(0 0 0 / 0.08), 0 2px 4px -2px rgb(0 0 0 / 0.08);
    --bookinn-shadow-xl: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* Spacing Scale */
    --bookinn-space-1: 0.25rem;
    --bookinn-space-2: 0.5rem;
    --bookinn-space-3: 0.75rem;
    --bookinn-space-4: 1rem;
    --bookinn-space-5: 1.25rem;
    --bookinn-space-6: 1.5rem;
    --bookinn-space-8: 2rem;
    --bookinn-space-10: 2.5rem;
    --bookinn-space-12: 3rem;
    
    /* Typography */
    --bookinn-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --bookinn-text-xs: 0.75rem;
    --bookinn-text-sm: 0.875rem;
    --bookinn-text-base: 1rem;
    --bookinn-text-lg: 1.125rem;
    --bookinn-text-xl: 1.25rem;
    --bookinn-text-2xl: 1.5rem;
    --bookinn-text-3xl: 1.875rem;
    
    /* Border Radius */
    --bookinn-radius: 0.375rem;
    --bookinn-radius-md: 0.5rem;
    --bookinn-radius-lg: 0.75rem;
    --bookinn-radius-xl: 1rem;
    
    /* Shadows */
    --bookinn-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --bookinn-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --bookinn-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --bookinn-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --bookinn-transition: all 0.15s ease-in-out;
    --bookinn-transition-fast: all 0.1s ease-in-out;
    --bookinn-transition-slow: all 0.3s ease-in-out;
    
    /* Z-index Scale */
    --bookinn-z-dropdown: 1000;
    --bookinn-z-sticky: 1020;
    --bookinn-z-fixed: 1030;
    --bookinn-z-modal-backdrop: 1040;
    --bookinn-z-modal: 1050;
    --bookinn-z-popover: 1060;
    --bookinn-z-tooltip: 1070;
}

/* ===== BASE STYLES ===== */
.bookinn-management-widget,
.bookinn-dashboard-container {
    font-family: var(--bookinn-font-family) !important;
    font-size: var(--bookinn-text-base) !important;
    line-height: 1.5 !important;
    color: var(--bookinn-text-primary) !important;
    background-color: var(--bookinn-bg-primary) !important;
    border-radius: var(--bookinn-radius-lg) !important;
    box-shadow: var(--bookinn-shadow-md) !important;
    overflow: hidden !important;
    min-height: 600px !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.bookinn-management-widget *,
.bookinn-dashboard-container * {
    box-sizing: border-box;
}

/* ===== LAYOUT COMPONENTS ===== */
.bookinn-management-interface {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 600px;
}

/* Dashboard Header - Management Focused */
.bookinn-dashboard-header {
    background: #274690 !important;
    color: #fff !important;
    padding: 32px 32px 24px 32px !important;
    border-radius: var(--bookinn-radius-lg) var(--bookinn-radius-lg) 0 0 !important;
    box-shadow: var(--bookinn-shadow-lg) !important;
    margin-bottom: 0 !important;
    position: static !important;
    border-bottom: none !important;
}

.bookinn-header-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.bookinn-dashboard-title {
    font-size: 24px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    color: #fff !important;
}

.bookinn-header-actions {
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
}

.bookinn-user-menu {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    color: #fff !important;
}

.bookinn-user-name {
    font-weight: 500 !important;
    color: #fff !important;
}

/* Navigation Tabs - WordPress Admin Style Match */
.bookinn-tab-navigation {
    background: #f8f9fa !important;
    border-bottom: 1px solid #e9ecef !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: auto !important;
    white-space: nowrap !important;
    display: flex !important;
    color: #555d66 !important;
    box-shadow: none !important;
    position: static !important;
    width: 100% !important;
    min-height: auto !important;
    align-items: flex-end !important;
    z-index: 1 !important; /* Keep navigation below modals */
}

/* Ensure all navigation elements stay below modals */
.bookinn-tab-navigation *,
.bookinn-nav-container *,
.bookinn-tab-link,
.bookinn-tab-item {
    z-index: 1 !important;
}

.bookinn-nav-container {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 32px !important;
    position: relative !important;
    gap: 0 !important;
}

/* Tab Links - WordPress Admin Style Match */
.bookinn-tab-navigation .bookinn-tab-link {
    display: inline-block !important;
    background: #f6f7f7 !important;
    border: 1px solid #e2e2e2 !important;
    border-bottom: none !important;
    padding: 16px 24px !important;
    cursor: pointer !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #555d66 !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    outline: none !important;
    border-radius: 8px 8px 0 0 !important;
    margin-right: 2px !important;
    text-decoration: none !important;
    top: 2px !important;
    white-space: nowrap !important;
    min-height: auto !important;
    box-sizing: border-box !important;
}

.bookinn-tab-navigation .bookinn-tab-link:hover {
    color: #2271b1 !important;
    background: rgba(102, 126, 234, 0.08) !important;
}

.bookinn-tab-navigation .bookinn-tab-link.active {
    color: #2271b1 !important;
    background: white !important;
    border-bottom: 2px solid #fff !important;
    box-shadow: 0 2px 8px rgba(102,126,234,0.07) !important;
    z-index: 2 !important;
    font-weight: 500 !important;
}

.bookinn-tab-navigation .bookinn-tab-link:focus {
    outline: 2px solid #667eea !important;
    outline-offset: 2px !important;
    z-index: 3 !important;
}

.bookinn-tab-navigation .bookinn-tab-link i {
    font-size: 16px !important;
    color: inherit !important;
    margin-right: 6px !important;
}

.bookinn-tab-navigation .bookinn-tab-link span {
    color: inherit !important;
    font-weight: inherit !important;
}

/* Navigation Toggle Button - Mobile Menu */
.bookinn-nav-toggle {
    display: none !important;
    background: transparent !important;
    border: none !important;
    padding: 8px !important;
    color: #555d66 !important;
    cursor: pointer !important;
    border-radius: 4px !important;
    transition: all 0.3s ease !important;
    margin-left: auto !important;
    outline: none !important;
    font-size: 16px !important;
    width: auto !important;
    height: auto !important;
}

.bookinn-nav-toggle:hover {
    background: rgba(102, 126, 234, 0.08) !important;
    color: #2271b1 !important;
}

.bookinn-nav-toggle:focus {
    outline: 2px solid #667eea !important;
    outline-offset: 2px !important;
}

.bookinn-nav-toggle i {
    font-size: 16px !important;
    color: inherit !important;
}

/* Hide toggle button on desktop, show on mobile */
@media (max-width: 768px) {
    .bookinn-nav-toggle {
        display: block !important;
    }

    .bookinn-nav-container {
        position: relative !important;
    }

    .bookinn-tab-link {
        display: none !important;
    }

    .bookinn-nav-container.mobile-open .bookinn-tab-link {
        display: flex !important;
        width: 100% !important;
        border-radius: 0 !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
}

/* Main Content Area - Compact Layout */
.bookinn-dashboard-content {
    display: grid !important;
    grid-template-columns: 1fr 280px !important;
    gap: 16px !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 16px !important;
    min-height: calc(85vh - 88px) !important;
}

.bookinn-main-area {
    background-color: white !important;
    border-radius: var(--bookinn-radius-lg) !important;
    box-shadow: var(--bookinn-shadow) !important;
    overflow: hidden !important;
    width: 100% !important;
    border: 1px solid var(--bookinn-border-light) !important;
}

/* ===== TAB NAVIGATION ===== */
.bookinn-tab-nav {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    background: linear-gradient(135deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%) !important;
    color: var(--bookinn-text-white) !important;
    padding: 0 !important;
    border-bottom: 1px solid var(--bookinn-border-light) !important;
    width: 100% !important;
    margin: 0 !important;
}

/* Legacy compatibility - both .bookinn-tabs and .bookinn-tab-links supported */
.bookinn-tabs,
.bookinn-tab-links {
    display: flex;
    align-items: center;
    flex: 1;
    list-style: none;
    margin: 0;
    padding: 0;
}

.bookinn-tab-item {
    margin: 0;
}

.bookinn-tab-link {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2);
    padding: var(--bookinn-space-4) var(--bookinn-space-6);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--bookinn-text-sm);
    transition: var(--bookinn-transition);
    border-bottom: 3px solid transparent;
    cursor: pointer;
}

.bookinn-tab-link:hover {
    color: var(--bookinn-text-white);
    background-color: rgba(255, 255, 255, 0.1);
}

.bookinn-tab-link.active,
.bookinn-tab-item.is-active .bookinn-tab-link {
    color: var(--bookinn-text-white);
    background-color: rgba(255, 255, 255, 0.15);
    border-bottom-color: rgba(255, 255, 255, 0.8);
}

.bookinn-tab-icon {
    font-size: var(--bookinn-text-lg);
}

/* ===== TAB CONTENT ===== */
/* Legacy compatibility - support multiple naming conventions */
.bookinn-tab-content,
.bookinn-tab-panel,
.bookinn-tab-pane {
    display: none;
    flex: 1;
    padding: var(--bookinn-space-6);
    background-color: var(--bookinn-bg-secondary);
    animation: bookinn-fadeIn 0.3s ease-in-out;
}

.bookinn-tab-content.active,
.bookinn-tab-panel.active,
.bookinn-tab-pane.active,
.bookinn-tab-pane.is-active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Tab panels container */
.bookinn-tab-panels {
    position: relative !important;
    flex: 1 !important;
    background-color: var(--bookinn-bg-secondary) !important;
    overflow-y: auto !important;
}

/* Metrics Cards - Professional Management Style */
.bookinn-metrics-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 16px !important;
    margin-bottom: 24px !important;
    position: relative !important;
}

.bookinn-metric-card {
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%) !important;
    padding: 16px !important;
    border-radius: var(--bookinn-radius-lg) !important;
    box-shadow: var(--bookinn-shadow) !important;
    border: 1px solid var(--bookinn-border-light) !important;
    transition: var(--bookinn-transition) !important;
    position: relative !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
}

.bookinn-metric-card:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--bookinn-shadow-lg) !important;
    border-color: var(--bookinn-primary) !important;
}

.bookinn-metric-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 4px !important;
    height: 100% !important;
    background: linear-gradient(to bottom, var(--bookinn-primary), var(--bookinn-accent)) !important;
}

.bookinn-metric-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: var(--bookinn-radius) !important;
    background: var(--bookinn-primary) !important;
    color: white !important;
    font-size: 18px !important;
    flex-shrink: 0 !important;
}

.bookinn-metric-content {
    flex: 1 !important;
}

.bookinn-metric-value {
    font-size: 24px !important;
    font-weight: 700 !important;
    color: var(--bookinn-text-primary) !important;
    margin: 0 0 4px 0 !important;
    line-height: 1.2 !important;
}

.bookinn-metric-label {
    font-size: 13px !important;
    color: var(--bookinn-text-secondary) !important;
    margin: 0 0 8px 0 !important;
    font-weight: 500 !important;
}

.bookinn-metric-change {
    font-size: 12px !important;
    font-weight: 600 !important;
    padding: 2px 6px !important;
    border-radius: 12px !important;
    display: inline-block !important;
}

.bookinn-metric-change.positive {
    background: rgba(5, 150, 105, 0.1) !important;
    color: #059669 !important;
}

.bookinn-metric-change.negative {
    background: rgba(220, 38, 38, 0.1) !important;
    color: #dc2626 !important;
}

/* Sidebar Styles - Proper Vertical Spacing */
.bookinn-sidebar {
    display: flex !important;
    flex-direction: column !important;
    gap: var(--bookinn-space-4) !important;
    padding: var(--bookinn-space-4) !important;
}

.bookinn-sidebar-widget {
    background: white !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
    border: 1px solid var(--bookinn-border-light) !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    margin-bottom: var(--bookinn-space-3) !important;
}

/* Proper spacing between sidebar cards */
.bookinn-sidebar-widget + .bookinn-sidebar-widget {
    margin-top: var(--bookinn-space-4) !important;
}

/* Subtle hover effect */
.bookinn-sidebar-widget:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.12) !important;
}

.bookinn-sidebar-widget:hover {
    border-color: var(--bookinn-primary) !important;
    transform: translateY(-1px) !important;
}

.bookinn-widget-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 12px 16px !important;
    background: linear-gradient(135deg, var(--bookinn-bg-tertiary) 0%, #fff 100%) !important;
    border-bottom: 1px solid var(--bookinn-border-light) !important;
    font-weight: 600 !important;
    color: var(--bookinn-text-primary) !important;
}

.bookinn-widget-header h4 {
    margin: 0 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: var(--bookinn-text-primary) !important;
}

.bookinn-widget-count {
    background: var(--bookinn-primary) !important;
    color: white !important;
    padding: 2px 8px !important;
    border-radius: 12px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    min-width: 20px !important;
    text-align: center !important;
}

.bookinn-widget-content {
    padding: 16px !important;
}

.bookinn-quick-actions {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 8px !important;
}

.bookinn-quick-action {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 6px !important;
    padding: 12px 8px !important;
    background: white !important;
    border: 1px solid var(--bookinn-border-light) !important;
    border-radius: var(--bookinn-radius) !important;
    color: var(--bookinn-text-primary) !important;
    text-decoration: none !important;
    transition: var(--bookinn-transition) !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-align: center !important;
    min-height: 64px !important;
}

.bookinn-quick-action:hover {
    background: var(--bookinn-primary) !important;
    color: white !important;
    border-color: var(--bookinn-primary) !important;
    transform: translateY(-2px) !important;
}

/* Green color only for New Booking button */
.bookinn-quick-action#quick-new-booking {
    background: var(--bookinn-success) !important;
    color: white !important;
    border-color: var(--bookinn-success) !important;
}

.bookinn-quick-action#quick-new-booking:hover {
    background: #15803d !important;
    border-color: #15803d !important;
    transform: translateY(-2px) !important;
}

.bookinn-quick-action i {
    font-size: 16px !important;
    color: inherit !important;
}

.bookinn-arrivals-list {
    max-height: 200px !important;
    overflow-y: auto !important;
}

.bookinn-arrival-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid var(--bookinn-border-light) !important;
}

.bookinn-arrival-item:last-child {
    border-bottom: none !important;
}

.bookinn-arrival-guest {
    font-weight: 500 !important;
    color: var(--bookinn-text-primary) !important;
    font-size: 13px !important;
}

.bookinn-arrival-room {
    font-size: 12px !important;
    color: var(--bookinn-text-secondary) !important;
}

.bookinn-arrival-time {
    font-size: 12px !important;
    color: var(--bookinn-text-muted) !important;
}

/* Charts Section - Professional Dashboard Style */
.bookinn-charts-section {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 16px !important;
    margin-bottom: 24px !important;
}

.bookinn-chart-container {
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%) !important;
    border-radius: var(--bookinn-radius-lg) !important;
    box-shadow: var(--bookinn-shadow) !important;
    border: 1px solid var(--bookinn-border-light) !important;
    overflow: hidden !important;
    transition: var(--bookinn-transition) !important;
}

.bookinn-chart-container:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--bookinn-shadow-lg) !important;
    border-color: var(--bookinn-primary) !important;
}

.bookinn-chart-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 16px 20px !important;
    background: linear-gradient(135deg, var(--bookinn-bg-tertiary) 0%, #fff 100%) !important;
    border-bottom: 1px solid var(--bookinn-border-light) !important;
}

.bookinn-chart-header h3,
.bookinn-chart-header h4 {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: var(--bookinn-text-primary) !important;
}

.bookinn-chart-controls {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.bookinn-chart-wrapper {
    padding: 20px !important;
    background: #fff !important;
    position: relative !important;
    min-height: 200px !important;
}

.bookinn-chart-wrapper canvas {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
}

/* Chart Cards - Compact Style */
.bookinn-chart-card {
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%) !important;
    border-radius: var(--bookinn-radius-lg) !important;
    box-shadow: var(--bookinn-shadow) !important;
    border: 1px solid var(--bookinn-border-light) !important;
    overflow: hidden !important;
    transition: var(--bookinn-transition) !important;
    margin-bottom: 16px !important;
}

.bookinn-chart-card:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--bookinn-shadow-lg) !important;
}

.bookinn-chart-card .bookinn-chart-header {
    padding: 12px 16px !important;
    background: linear-gradient(135deg, var(--bookinn-bg-tertiary) 0%, #fff 100%) !important;
}

.bookinn-chart-card .bookinn-chart-header h3 {
    font-size: 14px !important;
    font-weight: 600 !important;
}

.bookinn-chart-card .bookinn-chart-container {
    padding: 16px !important;
    background: #fff !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

/* Booking Analytics Section */
.bookinn-booking-analytics {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 16px !important;
    flex: 1 !important;
}

/* Dashboard Bottom Row */
.bookinn-dashboard-bottom-row {
    display: grid !important;
    grid-template-columns: 300px 1fr !important;
    gap: 16px !important;
    margin-top: 24px !important;
}

/* Recent Activity - Compact Style */
.bookinn-recent-activity {
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%) !important;
    border-radius: var(--bookinn-radius-lg) !important;
    box-shadow: var(--bookinn-shadow) !important;
    border: 1px solid var(--bookinn-border-light) !important;
    overflow: hidden !important;
}

.bookinn-recent-activity.bookinn-compact {
    max-height: 400px !important;
}

.bookinn-activity-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 12px 16px !important;
    background: linear-gradient(135deg, var(--bookinn-bg-tertiary) 0%, #fff 100%) !important;
    border-bottom: 1px solid var(--bookinn-border-light) !important;
}

.bookinn-activity-header h3 {
    margin: 0 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: var(--bookinn-text-primary) !important;
}

.bookinn-activity-list {
    padding: 16px !important;
    max-height: 300px !important;
    overflow-y: auto !important;
}

.bookinn-activity-item {
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid var(--bookinn-border-light) !important;
}

.bookinn-activity-item:last-child {
    border-bottom: none !important;
}

.bookinn-activity-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    background: var(--bookinn-primary) !important;
    color: white !important;
    font-size: 14px !important;
    flex-shrink: 0 !important;
}

.bookinn-activity-content {
    flex: 1 !important;
}

.bookinn-activity-title {
    font-weight: 500 !important;
    color: var(--bookinn-text-primary) !important;
    font-size: 13px !important;
    margin: 0 0 4px 0 !important;
}

.bookinn-activity-description {
    font-size: 12px !important;
    color: var(--bookinn-text-secondary) !important;
    margin: 0 0 4px 0 !important;
}

.bookinn-activity-time {
    font-size: 11px !important;
    color: var(--bookinn-text-muted) !important;
}

/* ===== SUB-TAB NAVIGATION ===== */
.bookinn-sub-tab-nav {
    display: flex;
    gap: var(--bookinn-space-1);
    margin-bottom: var(--bookinn-space-6);
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-sub-tab-link {
    padding: var(--bookinn-space-3) var(--bookinn-space-4);
    color: var(--bookinn-text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--bookinn-text-sm);
    border-bottom: 2px solid transparent;
    transition: var(--bookinn-transition);
    cursor: pointer;
}

.bookinn-sub-tab-link:hover {
    color: var(--bookinn-text-primary);
    border-bottom-color: var(--bookinn-border-medium);
}

.bookinn-sub-tab-link.active {
    color: var(--bookinn-primary);
    border-bottom-color: var(--bookinn-primary);
}

.bookinn-sub-tab-panel {
    display: none;
}

.bookinn-sub-tab-panel.active {
    display: block;
}

/* ===== UNIFIED MODAL SYSTEM ===== */
.bookinn-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 999999 !important; /* Extremely high z-index to ensure visibility */
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    backdrop-filter: blur(2px) !important;
    -webkit-backdrop-filter: blur(2px) !important;
}

.bookinn-modal.is-active {
    display: flex !important;
    opacity: 1 !important;
}

/* Accessibility improvements - ensure modals work without aria-hidden conflicts */
.bookinn-modal:not(.is-active) {
    display: none !important;
    visibility: hidden !important;
}

.bookinn-modal.is-active {
    display: flex !important;
    visibility: visible !important;
}

/* Force visibility for modals shown by ModalManager */
.bookinn-modal[style*="display: flex"] {
    display: flex !important;
    opacity: 1 !important;
    z-index: 999999 !important;
}

.bookinn-modal-content {
    background: var(--bookinn-bg-primary) !important;
    border-radius: var(--bookinn-radius-xl) !important;
    box-shadow: var(--bookinn-shadow-xl) !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
    min-width: 480px !important;
    width: auto !important;
    position: relative !important;
    transform: scale(0.95) !important;
    transition: transform 0.3s ease !important;
    overflow: hidden !important;
    border: 1px solid var(--bookinn-border-light) !important;
    z-index: 1000000 !important; /* Even higher for content */
}

.bookinn-modal.is-active .bookinn-modal-content,
.bookinn-modal[style*="display: flex"] .bookinn-modal-content {
    transform: scale(1) !important;
    z-index: 1000000 !important;
}

/* Modal Sizes */
.bookinn-modal-sm .bookinn-modal-content { max-width: 400px; }
.bookinn-modal-md .bookinn-modal-content { max-width: 600px; }
.bookinn-modal-lg .bookinn-modal-content { max-width: 800px; }
.bookinn-modal-xl .bookinn-modal-content { max-width: 1200px; }

/* Wizard Modal Specific Styles */
.bookinn-wizard-modal .bookinn-modal-content {
    max-width: 1000px;
    min-height: 600px;
}

.bookinn-wizard-steps {
    display: flex;
    justify-content: center;
    padding: 20px;
    background: var(--bookinn-bg-secondary);
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 20px;
    margin: 0 10px;
    border-radius: var(--bookinn-radius);
    transition: var(--bookinn-transition);
    cursor: pointer;
    min-width: 120px;
}

.bookinn-step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--bookinn-border-medium);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 8px;
    transition: var(--bookinn-transition);
}

.bookinn-step-title {
    font-size: 12px;
    font-weight: 500;
    color: var(--bookinn-text-secondary);
    text-align: center;
}

.bookinn-step.active .bookinn-step-number {
    background: var(--bookinn-primary);
}

.bookinn-step.active .bookinn-step-title {
    color: var(--bookinn-primary);
    font-weight: 600;
}

.bookinn-step.completed .bookinn-step-number {
    background: var(--bookinn-success);
}

.bookinn-step.completed .bookinn-step-title {
    color: var(--bookinn-success);
}

.bookinn-wizard-content {
    padding: 30px;
    min-height: 400px;
}

.bookinn-wizard-step {
    display: none;
}

.bookinn-wizard-step.active {
    display: block;
}

.bookinn-wizard-step h3 {
    margin: 0 0 20px 0;
    color: var(--bookinn-text-primary);
    font-size: 20px;
    font-weight: 600;
}

/* Modal Centering - Enhanced from old dashboard - CONSOLIDATED */

.bookinn-modal-content {
    background: white;
    border-radius: var(--bookinn-radius-xl);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    max-width: 90vw;
    overflow: hidden;
    margin: var(--bookinn-space-4);
    width: 100%;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
    border: 1px solid var(--bookinn-border-light);
}

.bookinn-modal.is-active .bookinn-modal-content {
    transform: scale(1);
}

/* Compact Modal Styles */
.bookinn-modal-compact .bookinn-modal-content {
    max-width: 500px;
    min-width: 450px;
}

.bookinn-modal-compact .bookinn-modal-header {
    padding: var(--bookinn-space-3) var(--bookinn-space-4);
    min-height: 48px;
}

.bookinn-modal-compact .bookinn-modal-header h4 {
    font-size: 16px;
    font-weight: 500;
}

.bookinn-modal-compact .bookinn-modal-body {
    padding: var(--bookinn-space-4);
    max-height: 60vh;
}

.bookinn-modal-compact .bookinn-modal-footer {
    padding: var(--bookinn-space-3) var(--bookinn-space-4);
}

.bookinn-modal-compact .bookinn-form-grid {
    gap: var(--bookinn-space-3);
}

.bookinn-modal-compact .bookinn-form-group {
    margin-bottom: var(--bookinn-space-3);
}

.bookinn-modal-compact .bookinn-input,
.bookinn-modal-compact .bookinn-select,
.bookinn-modal-compact .bookinn-textarea {
    padding: var(--bookinn-space-2);
    font-size: 14px;
}

.bookinn-modal-compact label {
    font-size: 13px;
    margin-bottom: var(--bookinn-space-1);
}

/* Form Sections */
.bookinn-form-section {
    margin-bottom: var(--bookinn-space-5);
    padding-bottom: var(--bookinn-space-4);
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.bookinn-form-section h5 {
    margin: 0 0 var(--bookinn-space-3) 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--bookinn-text-primary);
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2);
}

.bookinn-form-section h5 i {
    color: var(--bookinn-primary);
    font-size: 14px;
}

.bookinn-form-group.full-width {
    grid-column: 1 / -1;
}

/* Edit Modal Form Layout */
.bookinn-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.bookinn-form-grid.single-column {
    grid-template-columns: 1fr;
}

.bookinn-form-field {
    display: flex;
    flex-direction: column;
}

.bookinn-form-field label {
    margin-bottom: 0.3rem;
    font-weight: 500;
    color: var(--bookinn-text-primary);
    font-size: 0.85rem;
}

.bookinn-form-field input,
.bookinn-form-field select,
.bookinn-form-field textarea {
    padding: 0.5rem;
    border: 1px solid var(--bookinn-border-light);
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.bookinn-form-field input:focus,
.bookinn-form-field select:focus,
.bookinn-form-field textarea:focus {
    outline: none;
    border-color: var(--bookinn-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.bookinn-form-field textarea {
    resize: vertical;
    min-height: 80px;
}

/* Booking Summary Styles */
.bookinn-booking-summary {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid var(--bookinn-border-light);
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.bookinn-booking-summary h5 {
    margin: 0 0 0.75rem 0;
    color: var(--bookinn-primary);
    font-size: 0.95rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    padding-bottom: 0.5rem;
}

.bookinn-summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.bookinn-summary-item:last-child {
    margin-bottom: 0;
    border-top: 1px solid rgba(59, 130, 246, 0.2);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-weight: 600;
}

.bookinn-summary-label {
    color: var(--bookinn-text-secondary);
    font-size: 0.85rem;
}

.bookinn-summary-value {
    color: var(--bookinn-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

/* Action Buttons Layout */
.bookinn-wizard-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid var(--bookinn-border-light);
    background: #f8fafc;
    gap: 1rem;
}

.bookinn-wizard-footer > div {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.bookinn-action-buttons {
    display: flex;
    gap: 0.75rem;
}

/* Button Sizing Optimizations */
.bookinn-btn {
    padding: 0.5rem 1rem;
    font-size: 13px !important; /* Reduced font size for consistency */
    border-radius: 6px !important; /* Consistent rounded corners */
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    box-shadow: none !important; /* Remove any shadows */
}

.bookinn-btn-secondary {
    background: #ffffff !important; /* White background */
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important; /* Rectangular with rounded corners */
}

.bookinn-btn-secondary:hover {
    background: #f9fafb !important; /* Light gray on hover */
    color: #1f2937 !important;
    border-color: #9ca3af !important;
}

.bookinn-btn-primary {
    background: var(--bookinn-primary);
    color: white;
}

.bookinn-btn-primary:hover {
    background: var(--bookinn-primary-hover);
    transform: translateY(-1px);
}

.bookinn-btn-danger {
    background: #dc2626;
    color: white;
}

.bookinn-btn-danger:hover {
    background: #b91c1c;
    transform: translateY(-1px);
}

/* Wizard Room Table Styles */
.bookinn-rooms-table-container {
    margin: 20px 0;
}

.bookinn-rooms-table-wrapper {
    overflow-x: auto;
    border: 1px solid var(--bookinn-border-light);
    border-radius: var(--bookinn-radius);
    background: white;
}

.bookinn-rooms-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.bookinn-rooms-table thead {
    background: var(--bookinn-bg-secondary);
}

.bookinn-rooms-table th {
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: var(--bookinn-text-primary);
    border-bottom: 1px solid var(--bookinn-border-light);
    white-space: nowrap;
}

.bookinn-rooms-table td {
    padding: 16px;
    border-bottom: 1px solid var(--bookinn-border-light);
    vertical-align: middle;
}

.bookinn-rooms-table tbody tr:hover {
    background: var(--bookinn-bg-secondary);
}

.bookinn-rooms-table tbody tr.selected {
    background: rgba(34, 197, 94, 0.1);
    border-left: 4px solid var(--bookinn-success);
}

.bookinn-room-number {
    font-weight: 600;
}

.bookinn-room-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-top: 4px;
}

.bookinn-room-status.available {
    background: rgba(34, 197, 94, 0.1);
    color: var(--bookinn-success);
}

.bookinn-room-type {
    color: var(--bookinn-text-secondary);
    font-weight: 500;
}

.bookinn-room-capacity {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--bookinn-text-secondary);
}

.bookinn-room-capacity i {
    color: var(--bookinn-primary);
}

.bookinn-room-amenities {
    color: var(--bookinn-text-secondary);
    font-size: 13px;
    max-width: 200px;
}

.bookinn-room-rate,
.bookinn-room-total {
    font-weight: 600;
    color: var(--bookinn-text-primary);
    text-align: right;
}

.bookinn-room-total {
    color: var(--bookinn-success);
    font-size: 16px;
}

.bookinn-room-action {
    text-align: center;
}

.bookinn-btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 28px;
}

/* Loading state */
.bookinn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 40px;
    color: var(--bookinn-text-secondary);
}

.bookinn-loading i {
    font-size: 18px;
}

/* No rooms message */
.bookinn-no-rooms {
    text-align: center;
    padding: 40px;
    color: var(--bookinn-text-secondary);
}

.bookinn-no-rooms i {
    font-size: 48px;
    color: var(--bookinn-border-medium);
    margin-bottom: 16px;
}

.bookinn-no-rooms h4 {
    margin: 0 0 8px 0;
    color: var(--bookinn-text-primary);
}

.bookinn-no-rooms p {
    margin: 0;
    font-size: 14px;
}

/* Booking Summary Styles */
.bookinn-booking-summary,
.bookinn-final-summary {
    margin: 20px 0;
}

.bookinn-summary-card,
.bookinn-final-summary-card {
    background: white;
    border: 1px solid var(--bookinn-border-light);
    border-radius: var(--bookinn-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.bookinn-summary-card h4,
.bookinn-final-summary-card h4 {
    margin: 0 0 16px 0;
    color: var(--bookinn-text-primary);
    font-size: 18px;
    font-weight: 600;
}

.bookinn-summary-section {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-summary-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.bookinn-summary-section h5 {
    margin: 0 0 12px 0;
    color: var(--bookinn-text-primary);
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bookinn-summary-section p {
    margin: 0 0 8px 0;
    color: var(--bookinn-text-secondary);
    font-size: 14px;
}

.bookinn-summary-section p:last-child {
    margin-bottom: 0;
}

.bookinn-summary-row,
.bookinn-price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-summary-row:last-child,
.bookinn-price-row:last-child {
    border-bottom: none;
}

.bookinn-summary-row.total,
.bookinn-price-row.total {
    border-top: 2px solid var(--bookinn-border-medium);
    margin-top: 8px;
    padding-top: 12px;
    font-size: 16px;
}

.bookinn-price-breakdown {
    background: var(--bookinn-bg-secondary);
    border-radius: var(--bookinn-radius);
    padding: 16px;
    margin-top: 12px;
}

/* Payment Form Styles */
.bookinn-payment-form {
    margin-top: 20px;
    padding: 20px;
    background: var(--bookinn-bg-secondary);
    border-radius: var(--bookinn-radius);
    border: 1px solid var(--bookinn-border-light);
}

/* Checkbox Styles */
.bookinn-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
}

.bookinn-checkbox-label input[type="checkbox"] {
    display: none;
}

.bookinn-checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid var(--bookinn-border-medium);
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--bookinn-transition);
    flex-shrink: 0;
    margin-top: 2px;
}

.bookinn-checkbox-label input[type="checkbox"]:checked + .bookinn-checkbox-custom {
    background: var(--bookinn-primary);
    border-color: var(--bookinn-primary);
}

.bookinn-checkbox-label input[type="checkbox"]:checked + .bookinn-checkbox-custom::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* ===== STANDARDIZED MODAL FORM STYLING ===== */
/* Apply View Booking modal styling to all modal forms */

/* Modal Container Standardization */
.bookinn-modal-container,
.bookinn-modal-content {
    background-color: var(--bookinn-bg-primary);
    border-radius: var(--bookinn-radius-xl);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--bookinn-border-light);
    overflow: hidden;
    position: relative;
    margin: auto;
    max-height: 90vh;
    max-width: 90vw;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.bookinn-modal-wide {
    max-width: 900px;
    width: 90vw;
}

/* Modal Header Standardization */
.bookinn-modal-header {
    background: linear-gradient(135deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%);
    color: white;
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
    border-bottom: none;
}

.bookinn-modal-header h4,
.bookinn-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.bookinn-modal-close,
.bookinn-close-modal {
    background: none !important;
    border: none !important;
    color: var(--bookinn-text-primary) !important;
    font-size: 24px !important;
    cursor: pointer !important;
    padding: 0 !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    transition: var(--bookinn-transition) !important;
    opacity: 0.8 !important;
    position: relative !important;
    z-index: 1000001 !important; /* Higher than modal content */
}

.bookinn-modal-close:hover,
.bookinn-close-modal:hover {
    background: rgba(0, 0, 0, 0.1) !important;
    opacity: 1 !important;
    transform: scale(1.1) !important;
}

/* Ensure close button is always accessible */
.bookinn-modal-header .bookinn-close-modal {
    margin-left: auto !important;
    flex-shrink: 0 !important;
}

/* Modal Body Standardization */
.bookinn-modal-body {
    background-color: var(--bookinn-bg-primary);
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Custom scrollbar for modal body */
.bookinn-modal-body::-webkit-scrollbar {
    width: 6px;
}

.bookinn-modal-body::-webkit-scrollbar-track {
    background: var(--bookinn-bg-secondary);
    border-radius: 3px;
}

.bookinn-modal-body::-webkit-scrollbar-thumb {
    background: var(--bookinn-border-medium);
    border-radius: 3px;
}

.bookinn-modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--bookinn-border-dark);
}

/* Modal Footer Standardization */
.bookinn-modal-footer {
    background-color: var(--bookinn-bg-secondary);
    border-top: 1px solid var(--bookinn-border-light);
    padding: 16px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* Form Section Styling (View Booking Style) */
.bookinn-view-section,
.bookinn-form-section {
    background-color: var(--bookinn-bg-secondary);
    border-radius: var(--bookinn-radius);
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--bookinn-border-light);
}

.bookinn-view-section:last-child,
.bookinn-form-section:last-child {
    margin-bottom: 0;
}

.bookinn-view-section h5,
.bookinn-form-section h5 {
    margin: 0 0 16px 0;
    color: var(--bookinn-primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bookinn-view-section h5 i,
.bookinn-form-section h5 i {
    color: var(--bookinn-primary);
    font-size: 14px;
}

/* View Grid Layout */
.bookinn-view-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.bookinn-view-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.bookinn-view-item label {
    font-size: 12px;
    font-weight: 600;
    color: var(--bookinn-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bookinn-view-item span {
    font-size: 14px;
    color: var(--bookinn-text-primary);
    font-weight: 500;
}

/* Status Badge Styling */
.bookinn-status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bookinn-status-badge.pending {
    background: rgba(251, 191, 36, 0.1);
    color: #d97706;
}

.bookinn-status-badge.confirmed {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.bookinn-status-badge.checked_in {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

.bookinn-status-badge.checked_out {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.bookinn-status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

/* ===== MODAL BUTTON STANDARDIZATION ===== */
/* Fix Cancel button sizing and ensure consistency */

.bookinn-modal-footer .bookinn-btn {
    height: 40px !important;
    min-height: 40px;
    padding: 0 20px;
    font-size: 13px !important; /* Reduced font size for consistency */
    font-weight: 500;
    border-radius: var(--bookinn-radius);
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--bookinn-transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    text-decoration: none;
    min-width: 100px;
    line-height: 1;
}

/* Primary buttons in modals */
.bookinn-modal-footer .bookinn-btn-primary {
    background-color: var(--bookinn-primary);
    color: white;
    border-color: var(--bookinn-primary);
}

.bookinn-modal-footer .bookinn-btn-primary:hover {
    background-color: var(--bookinn-primary-dark);
    border-color: var(--bookinn-primary-dark);
    transform: translateY(-1px);
}

/* Secondary buttons (Cancel) in modals */
.bookinn-modal-footer .bookinn-btn-secondary {
    background-color: #ffffff !important; /* White background */
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important; /* Consistent rounded corners */
}

.bookinn-modal-footer .bookinn-btn-secondary:hover {
    background-color: #f9fafb !important; /* Light gray on hover */
    color: #1f2937 !important;
    border-color: #9ca3af !important;
    transform: translateY(-1px);
}

/* Success buttons in modals */
.bookinn-modal-footer .bookinn-btn-success {
    background-color: var(--bookinn-success);
    color: white;
    border-color: var(--bookinn-success);
}

.bookinn-modal-footer .bookinn-btn-success:hover {
    background-color: #15803d;
    border-color: #15803d;
    transform: translateY(-1px);
}

/* Danger buttons in modals */
.bookinn-modal-footer .bookinn-btn-danger {
    background-color: var(--bookinn-danger);
    color: white;
    border-color: var(--bookinn-danger);
}

.bookinn-modal-footer .bookinn-btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
}

/* Disabled state for modal buttons */
.bookinn-modal-footer .bookinn-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Ensure buttons are always visible in modals */
.bookinn-modal-footer .bookinn-btn,
.bookinn-modal-body .bookinn-btn {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1;
}

/* Button icons in modals */
.bookinn-modal-footer .bookinn-btn i {
    font-size: 12px;
}

/* Button loading state */
.bookinn-modal-footer .bookinn-btn.loading {
    position: relative;
    color: transparent;
}

.bookinn-modal-footer .bookinn-btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.bookinn-modal-header {
    background: linear-gradient(135deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%);
    color: var(--bookinn-text-white);
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
    border-radius: var(--bookinn-radius-xl) var(--bookinn-radius-xl) 0 0;
}

.bookinn-modal-header h4,
.bookinn-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.bookinn-close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--bookinn-transition);
    opacity: 0.8;
}

.bookinn-close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
    opacity: 1;
    transform: scale(1.1);
}

.bookinn-close-modal:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

.bookinn-close-modal {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--bookinn-text-xl);
    cursor: pointer;
    padding: var(--bookinn-space-2);
    border-radius: var(--bookinn-radius);
    transition: var(--bookinn-transition);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bookinn-close-modal:hover {
    color: var(--bookinn-text-white);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Close wizard button styles - for X button in header */
.bookinn-wizard-header .bookinn-close-wizard {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--bookinn-text-white);
    cursor: pointer;
    padding: var(--bookinn-space-2);
    border-radius: var(--bookinn-radius);
    transition: var(--bookinn-transition);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bookinn-wizard-header .bookinn-close-wizard:hover {
    color: var(--bookinn-text-white);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Cancel button in footer should follow normal button styles */
.bookinn-wizard-footer .bookinn-close-wizard {
    background: #e5e7eb !important;
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    width: auto !important;
    height: auto !important;
    font-size: 0.85rem !important;
    padding: 0.5rem 1rem !important;
    display: inline-flex !important;
}

.bookinn-modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Duplicate scrollbar styles removed - consolidated above */

/* Duplicate modal footer removed - consolidated above */

/* Prevent body scroll when modal is open - preserve scrollbar */
body.bookinn-modal-open {
    overflow: hidden;
    padding-right: var(--bookinn-scrollbar-width, 0px);
}

/* Calculate and store scrollbar width */
body.bookinn-modal-open {
    --bookinn-scrollbar-width: calc(100vw - 100%);
}

/* ===== FORM COMPONENTS ===== */
.bookinn-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--bookinn-space-4);
    margin-bottom: var(--bookinn-space-4);
}

.bookinn-form-group {
    display: flex;
    flex-direction: column;
    gap: var(--bookinn-space-2);
}

.bookinn-form-group.full-width {
    grid-column: 1 / -1;
}

.bookinn-form-group label {
    font-weight: 500;
    font-size: 13px;
    color: var(--bookinn-text-primary);
    margin-bottom: var(--bookinn-space-1);
    display: block;
}

.bookinn-input,
.bookinn-select,
.bookinn-textarea {
    padding: var(--bookinn-space-2);
    border: 1px solid var(--bookinn-border-medium);
    border-radius: var(--bookinn-radius);
    font-size: 13px;
    transition: var(--bookinn-transition);
    background-color: var(--bookinn-bg-primary);
}

.bookinn-input:focus,
.bookinn-select:focus,
.bookinn-textarea:focus {
    outline: none;
    border-color: var(--bookinn-primary);
    box-shadow: 0 0 0 3px rgba(39, 70, 144, 0.1);
}

/* Enhanced form styles for better compatibility */
.bookinn-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px 16px;
    padding-right: 32px;
    appearance: none;
    background-color: white !important;
    color: #333333 !important;
}

.bookinn-form-group label.required::after {
    content: " *";
    color: var(--bookinn-error);
    font-weight: bold;
}

.bookinn-input::placeholder,
.bookinn-textarea::placeholder {
    color: var(--bookinn-text-muted);
    font-style: italic;
}

/* Form validation states */
.bookinn-input.error,
.bookinn-select.error,
.bookinn-textarea.error {
    border-color: var(--bookinn-error);
    background-color: rgba(220, 38, 38, 0.05);
}

.bookinn-input.success,
.bookinn-select.success,
.bookinn-textarea.success {
    border-color: var(--bookinn-success);
    background-color: rgba(5, 150, 105, 0.05);
}

/* Form actions */
.bookinn-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--bookinn-space-3);
    margin-top: var(--bookinn-space-6);
    padding-top: var(--bookinn-space-4);
    border-top: 1px solid var(--bookinn-border-light);
}

/* Small form elements */
.bookinn-select-sm {
    padding: 6px 12px;
    font-size: 12px;
    border: 1px solid var(--bookinn-border-medium);
    border-radius: 4px;
    background: white;
}

.bookinn-textarea {
    resize: vertical;
    min-height: 80px;
}

/* ===== BUTTON COMPONENTS ===== */
.bookinn-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--bookinn-space-1);
    padding: var(--bookinn-space-2) var(--bookinn-space-3);
    font-size: 13px;
    font-weight: 500;
    border: none;
    border-radius: var(--bookinn-radius);
    cursor: pointer;
    transition: var(--bookinn-transition);
    text-decoration: none;
    min-height: 32px;
}

.bookinn-btn-primary {
    background-color: var(--bookinn-primary);
    color: var(--bookinn-text-white);
}

.bookinn-btn-primary:hover {
    background-color: var(--bookinn-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--bookinn-shadow-md);
}

.bookinn-btn-secondary {
    background-color: var(--bookinn-bg-secondary);
    color: var(--bookinn-text-primary);
    border: 1px solid var(--bookinn-border-medium);
}

.bookinn-btn-secondary:hover {
    background-color: var(--bookinn-bg-tertiary);
    border-color: var(--bookinn-border-dark);
}

/* Green only for New buttons */
.bookinn-btn-success {
    background-color: var(--bookinn-success);
    color: white;
}

.bookinn-btn-success:hover {
    background-color: #15803d;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.4);
}

.bookinn-btn-danger {
    background-color: var(--bookinn-secondary);
    color: white;
}

.bookinn-btn-danger:hover {
    background-color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(100, 116, 139, 0.4);
}

/* ===== HEADER BUTTON COLOR STANDARDIZATION ===== */
/* Ensure proper color scheme without inline style conflicts */

/* Apply Filters button - Navy blue (#1e40af) */
#apply-filters,
.bookinn-btn-apply-filters,
button[data-action="apply-filters"] {
    background-color: #1e40af !important;
    color: white !important;
    border-color: #1e40af !important;
    box-shadow: none !important;
}

#apply-filters:hover,
.bookinn-btn-apply-filters:hover,
button[data-action="apply-filters"]:hover {
    background-color: #1e3a8a !important;
    border-color: #1e3a8a !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.4) !important;
}

/* New Booking buttons - Green (#16a34a) */
#bookinn-add-booking,
#quick-new-booking,
.bookinn-btn-new-booking,
button[data-action="new-booking"],
.bookinn-section-actions .bookinn-btn-primary[id*="add"],
.bookinn-section-actions .bookinn-btn-primary[id*="new"] {
    background-color: #16a34a !important;
    color: white !important;
    border-color: #16a34a !important;
    box-shadow: none !important;
}

#bookinn-add-booking:hover,
#quick-new-booking:hover,
.bookinn-btn-new-booking:hover,
button[data-action="new-booking"]:hover,
.bookinn-section-actions .bookinn-btn-primary[id*="add"]:hover,
.bookinn-section-actions .bookinn-btn-primary[id*="new"]:hover {
    background-color: #15803d !important;
    border-color: #15803d !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.4) !important;
}

/* Edit booking functionality removed */

/* Override any conflicting styles */
.bookinn-section-header .bookinn-btn,
.bookinn-section-actions .bookinn-btn,
.bookinn-filter-actions .bookinn-btn {
    font-weight: 500 !important;
    font-size: 14px !important;
    padding: 10px 16px !important;
    border-radius: var(--bookinn-radius) !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
}

/* ===== CONTRAST AND READABILITY FIXES ===== */
/* Only fix dropdown and select visibility - keep original button colors */

/* Fix dropdown and select text contrast */
.bookinn-select option {
    background-color: white !important;
    color: #333333 !important;
    padding: 8px 12px !important;
}

/* Fix dropdown menus and navigation */
.dropdown-menu,
.bookinn-dropdown-menu {
    background-color: white !important;
    border: 1px solid #ddd !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

.dropdown-menu li a,
.dropdown-menu .dropdown-item,
.bookinn-dropdown-menu li a,
.bookinn-dropdown-menu .dropdown-item {
    color: #333333 !important;
    padding: 8px 16px !important;
    background-color: transparent !important;
}

.dropdown-menu li a:hover,
.dropdown-menu .dropdown-item:hover,
.bookinn-dropdown-menu li a:hover,
.bookinn-dropdown-menu .dropdown-item:hover {
    background-color: #f8f9fa !important;
    color: #2271b1 !important;
}

/* Fix button dropdown toggles in forms only */
.modal-content .btn-group .dropdown-toggle,
.modal-content .bookinn-btn-group .dropdown-toggle,
.bookinn-form .btn-group .dropdown-toggle,
.bookinn-form .bookinn-btn-group .dropdown-toggle {
    color: #333333 !important;
    background-color: white !important;
    border: 1px solid #ddd !important;
}

.modal-content .btn-group .dropdown-toggle:hover,
.modal-content .bookinn-btn-group .dropdown-toggle:hover,
.bookinn-form .btn-group .dropdown-toggle:hover,
.bookinn-form .bookinn-btn-group .dropdown-toggle:hover {
    background-color: #f8f9fa !important;
    border-color: #2271b1 !important;
    color: #2271b1 !important;
}

/* Fix select elements specifically in forms */
.modal-content select,
.bookinn-form select,
.form-control,
select[id*="edit-"],
select[id*="room-"],
select[class*="bookinn-select"] {
    background-color: white !important;
    color: #333333 !important;
    border: 1px solid #ddd !important;
}

/* Force form label visibility */
.modal-content label,
.bookinn-form label,
.form-label,
.bookinn-form-label {
    color: #555d66 !important;
    font-weight: 500 !important;
}

/* Force input field visibility in forms */
.modal-content input[type="text"],
.modal-content input[type="email"],
.modal-content input[type="tel"],
.modal-content input[type="number"],
.modal-content input[type="date"],
.modal-content input[type="datetime-local"],
.modal-content textarea,
.bookinn-form input[type="text"],
.bookinn-form input[type="email"],
.bookinn-form input[type="tel"],
.bookinn-form input[type="number"],
.bookinn-form input[type="date"],
.bookinn-form input[type="datetime-local"],
.bookinn-form textarea {
    background-color: white !important;
    color: #333333 !important;
    border: 1px solid #ddd !important;
}

/* Force placeholder text visibility */
.modal-content input::placeholder,
.modal-content textarea::placeholder,
.bookinn-form input::placeholder,
.bookinn-form textarea::placeholder {
    color: #999999 !important;
    opacity: 1 !important;
}

/* Ensure icons in buttons are properly styled */
.bookinn-section-header .bookinn-btn i,
.bookinn-section-actions .bookinn-btn i,
.bookinn-filter-actions .bookinn-btn i {
    font-size: 12px !important;
    margin-right: 0 !important;
}

/* Remove any conflicting background or border styles */
.bookinn-btn[style*="background"],
.bookinn-btn[style*="border"],
.bookinn-btn[style*="color"] {
    background: inherit !important;
    border: inherit !important;
    color: inherit !important;
}

/* Focus states for accessibility */
#apply-filters:focus,
#bookinn-add-booking:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8) !important;
    outline-offset: 2px !important;
}

.bookinn-btn-sm {
    padding: var(--bookinn-space-2) var(--bookinn-space-3);
    font-size: var(--bookinn-text-xs);
    min-height: 32px;
}

.bookinn-btn-xs {
    padding: var(--bookinn-space-1) var(--bookinn-space-2);
    font-size: var(--bookinn-text-xs);
    min-height: 28px;
}

.bookinn-btn:disabled,
.bookinn-btn.bookinn-loading {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.bookinn-btn.bookinn-loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: bookinn-spin 1s linear infinite;
    margin-left: var(--bookinn-space-2);
}

/* Button size variants */
.bookinn-btn-sm {
    padding: var(--bookinn-space-2) var(--bookinn-space-3);
    font-size: var(--bookinn-text-sm);
    min-height: 32px;
}

.bookinn-btn-xs {
    padding: var(--bookinn-space-1) var(--bookinn-space-2);
    font-size: var(--bookinn-text-xs);
    min-height: 28px;
}

/* Ensure modal buttons are always visible - Critical for functionality */
.bookinn-modal-footer .bookinn-btn,
.bookinn-modal-body .bookinn-btn {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1;
}

/* Table action buttons */
.bookinn-table-actions .bookinn-btn {
    padding: var(--bookinn-space-1) var(--bookinn-space-2);
    font-size: var(--bookinn-text-xs);
    min-height: 28px;
}

.bookinn-table-actions .bookinn-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--bookinn-shadow);
}

/* JavaScript-controlled button states */
.bookinn-btn.bookinn-success {
    background-color: var(--bookinn-success) !important;
    color: white !important;
    border-color: var(--bookinn-success) !important;
}

.bookinn-btn.bookinn-success:hover {
    background-color: #059669 !important;
}

/* Room selection states */
.bookinn-room-card.selected {
    border-color: var(--bookinn-primary) !important;
    background-color: rgba(39, 70, 144, 0.05) !important;
    box-shadow: 0 0 0 2px rgba(39, 70, 144, 0.2) !important;
}

/* Step indicator states */
.bookinn-step.completed {
    background-color: var(--bookinn-success) !important;
    color: white !important;
}

.bookinn-step.completed::after {
    content: "✓" !important;
}

/* Calendar initialization state */
.fc-initialized {
    opacity: 1 !important;
    transition: opacity 0.5s ease !important;
}

/* Mobile navigation state */
.bookinn-nav-container.mobile-open .bookinn-tab-link {
    display: flex !important;
}

/* Icon buttons */
.bookinn-btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
    border: 1px solid var(--bookinn-border-medium);
    border-radius: var(--bookinn-radius);
    background-color: var(--bookinn-bg-secondary);
    color: var(--bookinn-text-primary);
    cursor: pointer;
    transition: var(--bookinn-transition);
    text-decoration: none;
}

.bookinn-btn-icon:hover {
    background-color: var(--bookinn-bg-tertiary);
    border-color: var(--bookinn-border-dark);
    transform: translateY(-1px);
    box-shadow: var(--bookinn-shadow-sm);
}

.bookinn-btn-icon:focus {
    outline: 2px solid var(--bookinn-primary);
    outline-offset: 2px;
}

/* Edit button specific styling */
.bookinn-edit-room,
.bookinn-edit-room-type {
    background-color: var(--bookinn-bg-secondary);
    color: var(--bookinn-text-primary);
    border: 1px solid var(--bookinn-border-medium);
}

.bookinn-edit-room:hover,
.bookinn-edit-room-type:hover {
    background-color: var(--bookinn-bg-tertiary);
    border-color: var(--bookinn-border-dark);
    color: var(--bookinn-text-primary);
}

/* Booking Edit Button Styling */
.bookinn-edit-booking {
    background-color: var(--bookinn-primary) !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: var(--bookinn-border-radius) !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 80px !important;
}

.bookinn-edit-booking:hover {
    background-color: var(--bookinn-primary-dark) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.bookinn-edit-booking:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.3) !important;
}

.bookinn-edit-booking:active {
    transform: translateY(0) !important;
}

/* Form Styles for Edit Modal */
.bookinn-form-input {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid var(--bookinn-border-light) !important;
    border-radius: var(--bookinn-border-radius) !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    background-color: white !important;
    color: var(--bookinn-text-primary) !important;
    transition: all 0.15s ease-in-out !important;
    box-sizing: border-box !important;
}

.bookinn-form-input:focus {
    outline: none !important;
    border-color: var(--bookinn-primary) !important;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1) !important;
}

.bookinn-form-input:disabled {
    background-color: var(--bookinn-gray-100) !important;
    color: var(--bookinn-text-muted) !important;
    cursor: not-allowed !important;
}

.bookinn-form-input.bookinn-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Edit modal specific styles */
.bookinn-view-item input.bookinn-form-input,
.bookinn-view-item select.bookinn-form-input,
.bookinn-view-item textarea.bookinn-form-input {
    margin-top: 4px !important;
    font-size: 14px !important;
}

.bookinn-view-item label {
    display: block !important;
    font-weight: 500 !important;
    color: var(--bookinn-text-secondary) !important;
    margin-bottom: 4px !important;
    font-size: 13px !important;
}

/* Ensure proper z-index for edit modal */
#bookinn-booking-edit-modal {
    z-index: 10050 !important;
}

#bookinn-booking-edit-modal .bookinn-modal-container {
    z-index: 10051 !important;
}

/* Notification styles */
.bookinn-notification {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    font-weight: 500 !important;
    border-radius: var(--bookinn-border-radius) !important;
}

/* Modal footer button spacing */
.bookinn-modal-footer .bookinn-edit-booking {
    margin-right: 8px !important;
}

/* ===== MODAL LOADING STATES ===== */
.bookinn-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: var(--bookinn-radius);
}

.bookinn-loading-spinner {
    text-align: center;
    color: var(--bookinn-text-secondary);
}

.bookinn-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--bookinn-border-light);
    border-top: 3px solid var(--bookinn-primary);
    border-radius: 50%;
    animation: bookinn-spin 1s linear infinite;
    margin: 0 auto var(--bookinn-space-3);
}

.bookinn-error-message {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: var(--bookinn-radius);
    padding: var(--bookinn-space-4);
    margin-bottom: var(--bookinn-space-4);
}

.bookinn-error-content {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-3);
    color: #dc2626;
}

.bookinn-error-content i {
    font-size: 20px;
    flex-shrink: 0;
}

.bookinn-error-content p {
    flex: 1;
    margin: 0;
    font-weight: 500;
}

/* ===== BOOKING MODAL SPECIFIC STYLES ===== */
.bookinn-modal-wide {
    max-width: 800px !important;
    width: 90% !important;
}

#bookinn-booking-modal .bookinn-modal-container,
#bookinn-booking-view-modal .bookinn-modal-container {
    background-color: var(--bookinn-bg-primary);
    opacity: 1;
    z-index: 10001;
    border-radius: var(--bookinn-border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

#bookinn-booking-modal .bookinn-modal-header,
#bookinn-booking-view-modal .bookinn-modal-header {
    background: linear-gradient(135deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%);
    color: white;
    border-bottom: none;
    padding: 20px 24px;
    border-radius: var(--bookinn-border-radius) var(--bookinn-border-radius) 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
}

#bookinn-booking-modal .bookinn-modal-header h4,
#bookinn-booking-view-modal .bookinn-modal-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

#bookinn-booking-modal .bookinn-modal-close,
#bookinn-booking-view-modal .bookinn-modal-close,
#bookinn-booking-modal .bookinn-close-modal,
#bookinn-booking-view-modal .bookinn-close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--bookinn-transition);
    opacity: 0.8;
}

#bookinn-booking-modal .bookinn-modal-close:hover,
#bookinn-booking-view-modal .bookinn-modal-close:hover,
#bookinn-booking-modal .bookinn-close-modal:hover,
#bookinn-booking-view-modal .bookinn-close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
    opacity: 1;
    transform: scale(1.1);
}

#bookinn-booking-modal .bookinn-modal-body,
#bookinn-booking-view-modal .bookinn-modal-body {
    background-color: var(--bookinn-bg-primary);
    opacity: 1;
    padding: var(--bookinn-space-6);
    max-height: 70vh;
    overflow-y: auto;
}

#bookinn-booking-modal .bookinn-modal-footer,
#bookinn-booking-view-modal .bookinn-modal-footer {
    background-color: var(--bookinn-bg-secondary);
    border-top: 1px solid var(--bookinn-border-light);
    padding: var(--bookinn-space-4) var(--bookinn-space-6);
    border-radius: 0 0 var(--bookinn-border-radius) var(--bookinn-border-radius);
}

#bookinn-booking-modal .bookinn-form {
    background-color: transparent;
    opacity: 1;
}

#bookinn-booking-modal .bookinn-input,
#bookinn-booking-modal .bookinn-select,
#bookinn-booking-modal .bookinn-textarea {
    background-color: var(--bookinn-bg-secondary);
    border: 1px solid var(--bookinn-border-medium);
    color: var(--bookinn-text-primary);
    opacity: 1;
    padding: var(--bookinn-space-3);
    border-radius: var(--bookinn-border-radius);
    width: 100%;
    font-size: 14px;
}

#bookinn-booking-modal .bookinn-input:focus,
#bookinn-booking-modal .bookinn-select:focus,
#bookinn-booking-modal .bookinn-textarea:focus {
    border-color: var(--bookinn-primary);
    box-shadow: 0 0 0 2px rgba(39, 70, 144, 0.1);
    outline: none;
}

#bookinn-booking-modal label,
#bookinn-room-modal label,
#bookinn-room-type-modal label {
    color: var(--bookinn-text-primary);
    font-weight: 500;
    margin-bottom: var(--bookinn-space-2);
    display: block;
    font-size: 14px;
}

/* ===== BOOKING EDIT MODAL SPECIFIC STYLES ===== */
#bookinn-booking-edit-modal {
    z-index: 10050 !important;
}

#bookinn-booking-edit-modal .bookinn-modal-container {
    background-color: var(--bookinn-bg-primary) !important;
    opacity: 1 !important;
    z-index: 10051 !important;
    border-radius: var(--bookinn-border-radius) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    max-width: 800px !important;
    width: 90vw !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
}

#bookinn-booking-edit-modal .bookinn-modal-header {
    background: linear-gradient(135deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%) !important;
    color: white !important;
    border-bottom: none !important;
    padding: var(--bookinn-space-4) var(--bookinn-space-6) !important;
    border-radius: var(--bookinn-border-radius) var(--bookinn-border-radius) 0 0 !important;
}

#bookinn-booking-edit-modal .bookinn-modal-header h4 {
    color: white !important;
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

#bookinn-booking-edit-modal .bookinn-modal-close {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
    line-height: 1 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

#bookinn-booking-edit-modal .bookinn-modal-close:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

#bookinn-booking-edit-modal .bookinn-modal-body {
    padding: var(--bookinn-space-6) !important;
    background-color: var(--bookinn-bg-primary) !important;
    max-height: calc(90vh - 140px) !important;
    overflow-y: auto !important;
}

#bookinn-booking-edit-modal .bookinn-modal-footer {
    background-color: var(--bookinn-bg-secondary) !important;
    border-top: 1px solid var(--bookinn-border-light) !important;
    padding: var(--bookinn-space-4) var(--bookinn-space-6) !important;
    border-radius: 0 0 var(--bookinn-border-radius) var(--bookinn-border-radius) !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: var(--bookinn-space-3) !important;
}

#bookinn-booking-edit-modal .bookinn-form {
    background-color: transparent !important;
    opacity: 1 !important;
}

#bookinn-booking-edit-modal .bookinn-form-input {
    background-color: var(--bookinn-bg-primary) !important;
    border: 1px solid var(--bookinn-border-light) !important;
    color: var(--bookinn-text-primary) !important;
    opacity: 1 !important;
    padding: 8px 12px !important;
    border-radius: var(--bookinn-border-radius) !important;
    width: 100% !important;
    font-size: 14px !important;
    transition: all 0.15s ease-in-out !important;
}

#bookinn-booking-edit-modal .bookinn-form-input:focus {
    border-color: var(--bookinn-primary) !important;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1) !important;
    outline: none !important;
}

#bookinn-booking-edit-modal .bookinn-form-input:disabled {
    background-color: var(--bookinn-gray-100) !important;
    color: var(--bookinn-text-muted) !important;
    cursor: not-allowed !important;
}

#bookinn-booking-edit-modal .bookinn-form-input.bookinn-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

#bookinn-booking-edit-modal label {
    color: var(--bookinn-text-secondary) !important;
    font-weight: 500 !important;
    margin-bottom: 4px !important;
    display: block !important;
    font-size: 13px !important;
}

/* Ensure edit button is visible and properly styled */
#bookinn-booking-view-modal .bookinn-edit-booking {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* ===== ROOM MODAL SPECIFIC STYLES ===== */
#bookinn-room-modal .bookinn-modal-container,
#bookinn-room-type-modal .bookinn-modal-container {
    background-color: var(--bookinn-bg-primary);
    opacity: 1;
    z-index: 10001;
    border-radius: var(--bookinn-border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

#bookinn-room-modal .bookinn-modal-header,
#bookinn-room-type-modal .bookinn-modal-header {
    background: linear-gradient(135deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%);
    color: white;
    border-bottom: none;
    padding: 20px 24px;
    border-radius: var(--bookinn-border-radius) var(--bookinn-border-radius) 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
}

#bookinn-room-modal .bookinn-modal-header h4,
#bookinn-room-type-modal .bookinn-modal-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

#bookinn-room-modal .bookinn-modal-close,
#bookinn-room-type-modal .bookinn-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--bookinn-transition);
    opacity: 0.8;
}

#bookinn-room-modal .bookinn-modal-close:hover,
#bookinn-room-type-modal .bookinn-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    opacity: 1;
    transform: scale(1.1);
}

#bookinn-room-modal .bookinn-modal-body,
#bookinn-room-type-modal .bookinn-modal-body {
    background-color: var(--bookinn-bg-primary);
    opacity: 1;
    padding: var(--bookinn-space-6);
    max-height: 70vh;
    overflow-y: auto;
}

#bookinn-room-modal .bookinn-modal-footer,
#bookinn-room-type-modal .bookinn-modal-footer {
    background-color: var(--bookinn-bg-secondary);
    border-top: 1px solid var(--bookinn-border-light);
    padding: var(--bookinn-space-4) var(--bookinn-space-6);
    border-radius: 0 0 var(--bookinn-border-radius) var(--bookinn-border-radius);
}

/* ===== BOOKING VIEW MODAL STYLES ===== */
.bookinn-booking-view-content,
.bookinn-booking-form-content,
.bookinn-room-form-content,
.bookinn-room-type-form-content {
    display: flex;
    flex-direction: column;
    gap: var(--bookinn-space-6);
}

.bookinn-view-section {
    background-color: var(--bookinn-bg-secondary);
    border-radius: var(--bookinn-border-radius);
    padding: var(--bookinn-space-4);
    border: 1px solid var(--bookinn-border-light);
    margin-bottom: 20px;
}

.bookinn-view-section:last-child {
    margin-bottom: 0;
}

.bookinn-view-section h5 {
    margin: 0 0 var(--bookinn-space-4) 0;
    color: var(--bookinn-primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2);
}

.bookinn-view-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--bookinn-space-4);
}

.bookinn-view-item {
    display: flex;
    flex-direction: column;
    gap: var(--bookinn-space-1);
}

.bookinn-view-item.full-width {
    grid-column: 1 / -1;
}

.bookinn-view-item label {
    font-size: 12px;
    font-weight: 600;
    color: var(--bookinn-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bookinn-view-item span {
    font-size: 14px;
    color: var(--bookinn-text-primary);
    font-weight: 500;
}

.bookinn-amount {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: var(--bookinn-primary) !important;
}

.bookinn-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px !important;
    font-weight: 600 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bookinn-status-pending {
    background-color: rgba(251, 191, 36, 0.1);
    color: #f59e0b;
}

.bookinn-status-confirmed {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.bookinn-status-checked_in {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.bookinn-status-checked_out {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.bookinn-status-cancelled {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.bookinn-status-no_show {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* ===== QUICK ACTION STYLES ===== */
.bookinn-active-filter {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2);
    padding: var(--bookinn-space-3);
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--bookinn-border-radius);
    margin-bottom: var(--bookinn-space-4);
    color: #3b82f6;
    font-size: 14px;
    font-weight: 500;
}

.bookinn-active-filter i {
    color: #3b82f6;
}

.bookinn-preselected-room {
    margin-bottom: var(--bookinn-space-4);
    padding: var(--bookinn-space-4);
    background-color: rgba(34, 197, 94, 0.05);
    border: 2px solid var(--bookinn-success);
    border-radius: var(--bookinn-border-radius);
}

.bookinn-preselected-room .bookinn-room-card {
    margin: 0;
    border: none;
    background-color: transparent;
}

.bookinn-preselected-room .bookinn-room-status {
    background-color: var(--bookinn-success);
    color: white;
}

.bookinn-preselected-room::before {
    content: '';
    display: block;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: var(--bookinn-success);
    margin-bottom: var(--bookinn-space-2);
}

.bookinn-preselected-room::before {
    content: '✓ Room Pre-selected for Quick Booking';
}

/* Ensure modal is always visible and not transparent */
.bookinn-modal.is-active {
    opacity: 1 !important;
    visibility: visible !important;
}

.bookinn-modal.is-active .bookinn-modal-container,
.bookinn-modal.is-active .bookinn-modal-content {
    opacity: 1 !important;
    transform: scale(1) !important;
}

/* ===== LIST FILTERS STYLES ===== */
.bookinn-list-filters,
#bookinn-list-filters {
    background-color: var(--bookinn-bg-secondary);
    padding: var(--bookinn-space-5);
    border-bottom: 1px solid var(--bookinn-border-light);
    margin-bottom: var(--bookinn-space-5);
    border-radius: var(--bookinn-radius);
    box-shadow: var(--bookinn-shadow);
    display: none; /* Hidden by default, shown via slideToggle */
}

.bookinn-list-filters.show,
#bookinn-list-filters.show {
    display: block;
}

.bookinn-list-filters .bookinn-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--bookinn-space-4);
    margin-bottom: var(--bookinn-space-4);
}

.bookinn-list-filters .bookinn-form-group {
    margin-bottom: 0;
}

.bookinn-list-filters .bookinn-btn {
    margin-top: var(--bookinn-space-2);
}

.bookinn-filter-actions {
    display: flex;
    gap: var(--bookinn-space-3);
    justify-content: flex-end;
    margin-top: var(--bookinn-space-4);
    padding-top: var(--bookinn-space-4);
    border-top: 1px solid var(--bookinn-border-light);
}

/* ===== BOOKING WIZARD STYLES ===== */
.bookinn-booking-wizard {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.6) !important;
    z-index: 10002 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    backdrop-filter: blur(3px) !important;
    -webkit-backdrop-filter: blur(3px) !important;
    padding: 20px !important;
    /* Fix scrollbar issue */
    overflow-y: auto !important;
}

.bookinn-booking-wizard.active {
    display: flex !important;
    opacity: 1 !important;
}

/* Ensure body scroll is preserved when wizard is open */
body.bookinn-wizard-open {
    overflow: visible !important;
    padding-right: 0 !important;
}

.bookinn-booking-wizard .bookinn-modal-content {
    background-color: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    max-width: 900px !important;
    width: 95% !important;
    max-height: 85vh !important;
    overflow-y: auto !important;
    transform: scale(0.9) !important;
    transition: transform 0.3s ease-in-out !important;
    border: 1px solid #e2e8f0 !important;
    /* Ensure modal content is scrollable */
    display: flex !important;
    flex-direction: column !important;
}

.bookinn-booking-wizard.active .bookinn-modal-content {
    transform: scale(1) !important;
}

.bookinn-wizard-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 24px 32px !important;
    border-bottom: 1px solid #e2e8f0 !important;
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%) !important;
    color: white !important;
    border-radius: 12px 12px 0 0 !important;
    flex-shrink: 0 !important;
}

.bookinn-wizard-header h4 {
    margin: 0 !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    color: white !important;
}

.bookinn-close-wizard {
    background: transparent !important;
    border: none !important;
    color: white !important;
    font-size: 24px !important;
    cursor: pointer !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    transition: background-color 0.2s ease !important;
    line-height: 1 !important;
}

.bookinn-close-wizard:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

#bookinn-booking-wizard {
    z-index: 10002 !important;
}

#bookinn-booking-wizard .bookinn-modal-content {
    background-color: var(--bookinn-bg-primary) !important;
    border-radius: var(--bookinn-border-radius-lg) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    max-width: 900px !important;
    width: 90% !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
}

.bookinn-wizard-steps {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: var(--bookinn-space-6) !important;
    padding: 0 var(--bookinn-space-4) !important;
    border-bottom: 1px solid var(--bookinn-border-light) !important;
    padding-bottom: var(--bookinn-space-4) !important;
    background: #f8fafc !important;
}

.bookinn-step {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    flex: 1 !important;
    position: relative !important;
    opacity: 0.5 !important;
    transition: opacity 0.3s ease !important;
    padding: 16px 8px !important;
}

.bookinn-step.active {
    opacity: 1 !important;
}

.bookinn-step:not(:last-child)::after {
    content: '' !important;
    position: absolute !important;
    top: 15px !important;
    right: -50% !important;
    width: 100% !important;
    height: 2px !important;
    background-color: var(--bookinn-border-light) !important;
    z-index: -1 !important;
}

.bookinn-step.active:not(:last-child)::after {
    background-color: var(--bookinn-primary) !important;
}

.bookinn-step-number {
    width: 30px !important;
    height: 30px !important;
    border-radius: 50% !important;
    background-color: var(--bookinn-border-light) !important;
    color: var(--bookinn-text-secondary) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    margin-bottom: var(--bookinn-space-2) !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
}

.bookinn-step.active .bookinn-step-number {
    background-color: var(--bookinn-primary) !important;
    color: white !important;
}

.bookinn-step.completed .bookinn-step-number {
    background-color: var(--bookinn-success) !important;
    color: white !important;
}

.bookinn-step.completed:not(:last-child)::after {
    background-color: var(--bookinn-success) !important;
}

.bookinn-step-title {
    font-size: 11px !important;
    font-weight: 500 !important;
    text-align: center !important;
    color: var(--bookinn-text-secondary) !important;
    line-height: 1.2 !important;
}

.bookinn-step.active .bookinn-step-title {
    color: var(--bookinn-text-primary) !important;
    font-weight: 600 !important;
}

.bookinn-step.completed .bookinn-step-title {
    color: var(--bookinn-success) !important;
}

.bookinn-wizard-content {
    padding: var(--bookinn-space-6) !important;
    min-height: 350px !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
}

.bookinn-wizard-step {
    display: none !important;
}

.bookinn-wizard-step.active {
    display: block !important;
}

.bookinn-wizard-step h3 {
    margin-bottom: var(--bookinn-space-4) !important;
    color: var(--bookinn-text-primary) !important;
    font-size: 20px !important;
    font-weight: 600 !important;
}

/* Wizard form styles with improved visibility */
.bookinn-wizard-step .bookinn-form-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: var(--bookinn-space-4) !important;
    margin-bottom: var(--bookinn-space-4) !important;
}

.bookinn-wizard-step .bookinn-form-group {
    margin-bottom: var(--bookinn-space-4) !important;
}

.bookinn-wizard-step .bookinn-form-group label {
    display: block !important;
    margin-bottom: var(--bookinn-space-2) !important;
    font-weight: 500 !important;
    color: var(--bookinn-text-primary) !important;
    font-size: 14px !important;
}

.bookinn-wizard-step .bookinn-input,
.bookinn-wizard-step .bookinn-select,
.bookinn-wizard-step .bookinn-textarea {
    width: 100% !important;
    padding: 12px 16px !important;
    border: 1px solid var(--bookinn-border-medium) !important;
    border-radius: var(--bookinn-radius) !important;
    font-size: 14px !important;
    transition: var(--bookinn-transition) !important;
    background-color: #ffffff !important;
    color: var(--bookinn-text-primary) !important;
}

.bookinn-wizard-step .bookinn-input:focus,
.bookinn-wizard-step .bookinn-select:focus,
.bookinn-wizard-step .bookinn-textarea:focus {
    outline: none !important;
    border-color: var(--bookinn-primary) !important;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1) !important;
}

/* Available rooms styling for wizard */
.bookinn-available-rooms {
    margin-top: var(--bookinn-space-4) !important;
}

.bookinn-rooms-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
    gap: var(--bookinn-space-4) !important;
    margin-top: var(--bookinn-space-4) !important;
}

.bookinn-room-card {
    border: 1px solid var(--bookinn-border-medium) !important;
    border-radius: var(--bookinn-radius) !important;
    padding: var(--bookinn-space-4) !important;
    background-color: #ffffff !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.bookinn-room-card:hover {
    border-color: var(--bookinn-primary) !important;
    box-shadow: 0 2px 8px rgba(30, 64, 175, 0.1) !important;
}

.bookinn-room-card.selected {
    border-color: var(--bookinn-primary) !important;
    background-color: rgba(30, 64, 175, 0.05) !important;
    box-shadow: 0 0 0 2px rgba(30, 64, 175, 0.2) !important;
}

.bookinn-room-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: var(--bookinn-space-3) !important;
}

.bookinn-room-header h4 {
    margin: 0 !important;
    color: var(--bookinn-text-primary) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
}

.bookinn-room-status {
    background-color: var(--bookinn-success) !important;
    color: white !important;
    padding: 2px 8px !important;
    border-radius: 12px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

.bookinn-room-info {
    margin-bottom: var(--bookinn-space-4) !important;
}

.bookinn-room-info p {
    margin: 0 0 var(--bookinn-space-2) 0 !important;
    color: var(--bookinn-text-secondary) !important;
    font-size: 14px !important;
}

.bookinn-room-info p:first-child {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: var(--bookinn-primary) !important;
}

.bookinn-room-actions {
    text-align: center !important;
}

/* Wizard actions */
.bookinn-wizard-actions {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: var(--bookinn-space-4) var(--bookinn-space-6) !important;
    border-top: 1px solid var(--bookinn-border-light) !important;
    background-color: var(--bookinn-bg-secondary) !important;
}

/* No rooms message */
.bookinn-no-rooms {
    text-align: center !important;
    padding: var(--bookinn-space-8) !important;
    color: var(--bookinn-text-secondary) !important;
}

.bookinn-no-rooms i {
    font-size: 48px !important;
    color: var(--bookinn-border-medium) !important;
    margin-bottom: var(--bookinn-space-4) !important;
}

.bookinn-no-rooms h4 {
    margin: 0 0 var(--bookinn-space-2) 0 !important;
    color: var(--bookinn-text-primary) !important;
}

.bookinn-no-rooms p {
    margin: 0 0 var(--bookinn-space-2) 0 !important;
}

/* Responsive design for wizard */
@media (max-width: 768px) {
    .bookinn-wizard-steps {
        flex-wrap: wrap !important;
        gap: var(--bookinn-space-2) !important;
    }

    .bookinn-step {
        flex: 0 0 calc(50% - var(--bookinn-space-1)) !important;
    }

    .bookinn-step:not(:last-child)::after {
        display: none !important;
    }

    .bookinn-wizard-step .bookinn-form-grid {
        grid-template-columns: 1fr !important;
    }

    .bookinn-rooms-grid {
        grid-template-columns: 1fr !important;
    }

    .bookinn-wizard-content {
        padding: var(--bookinn-space-4) !important;
    }
}

/* ===== TABLE COMPONENTS ===== */
.bookinn-table-container {
    background: var(--bookinn-bg-primary);
    border-radius: var(--bookinn-radius-lg);
    overflow: hidden;
    box-shadow: var(--bookinn-shadow);
}

.bookinn-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--bookinn-text-sm);
}

.bookinn-table th {
    background-color: var(--bookinn-bg-tertiary);
    color: var(--bookinn-text-primary);
    font-weight: 600;
    padding: var(--bookinn-space-4);
    text-align: left;
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-table td {
    padding: var(--bookinn-space-4);
    border-bottom: 1px solid var(--bookinn-border-light);
    vertical-align: middle;
}

.bookinn-table tbody tr:hover {
    background-color: var(--bookinn-bg-secondary);
}

.bookinn-table tbody tr:last-child td {
    border-bottom: none;
}

/* ===== STATUS COMPONENTS ===== */
.bookinn-status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--bookinn-space-1) var(--bookinn-space-3);
    font-size: var(--bookinn-text-xs);
    font-weight: 500;
    border-radius: var(--bookinn-radius);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.bookinn-status-available {
    background-color: rgba(22, 163, 74, 0.1);
    color: var(--bookinn-success);
}

.bookinn-status-maintenance {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--bookinn-warning);
}

.bookinn-status-out_of_order {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--bookinn-danger);
}

/* ===== UTILITY CLASSES ===== */
.bookinn-loading {
    text-align: center;
    color: var(--bookinn-text-muted);
    font-style: italic;
}

.bookinn-no-data {
    text-align: center;
    color: var(--bookinn-text-muted);
    padding: var(--bookinn-space-8);
}

.bookinn-error {
    text-align: center;
    color: var(--bookinn-danger);
    padding: var(--bookinn-space-4);
}

.bookinn-actions {
    display: flex;
    gap: var(--bookinn-space-2);
    align-items: center;
}

/* ===== ANIMATIONS ===== */
@keyframes bookinn-spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes bookinn-slideInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .bookinn-dashboard-content {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
        padding: 16px !important;
    }

    .bookinn-nav-container {
        padding: 0 16px !important;
    }

    .bookinn-header-content {
        padding: 0 16px !important;
    }

    .bookinn-metrics-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .bookinn-quick-actions {
        grid-template-columns: repeat(4, 1fr) !important;
    }

    .bookinn-sidebar {
        grid-column: 1 / -1 !important;
        grid-row: 1 !important;
    }

    .bookinn-main-area {
        grid-row: 2 !important;
    }
}

@media (max-width: 768px) {
    .bookinn-modal-content {
        min-width: 95vw;
        max-width: 95vw;
        margin: var(--bookinn-space-5);
        max-height: 85vh;
    }

    .bookinn-dashboard-container {
        margin: 8px 0 !important;
        border-radius: var(--bookinn-radius) !important;
    }

    .bookinn-nav-container {
        flex-wrap: wrap !important;
    }

    .bookinn-tab-link {
        display: none !important;
        width: 100% !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 0 !important;
        padding: 12px 16px !important;
    }

    .bookinn-dashboard-content {
        padding: 12px !important;
        gap: 12px !important;
    }

    .bookinn-tab-panel {
        padding: 16px !important;
    }

    .bookinn-header-content {
        flex-direction: column !important;
        gap: 8px !important;
        align-items: flex-start !important;
        padding: 8px 12px !important;
    }

    .bookinn-metrics-grid {
        grid-template-columns: 1fr !important;
        gap: 12px !important;
    }

    .bookinn-quick-actions {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px !important;
    }

    .bookinn-sidebar {
        gap: 1rem !important;
    }

    .bookinn-widget-content {
        padding: 1rem !important;
    }

    .bookinn-form-grid {
        grid-template-columns: 1fr;
        gap: var(--bookinn-space-3);
    }
    
    .bookinn-modal-header {
        padding: var(--bookinn-space-4);
    }
    
    .bookinn-modal-body {
        padding: var(--bookinn-space-4);
    }
    
    .bookinn-modal-footer {
        padding: var(--bookinn-space-3) var(--bookinn-space-4);
        flex-direction: column;
    }
    
    .bookinn-btn {
        width: 100%;
        justify-content: center;
    }
    
    .bookinn-tab-nav {
        flex-direction: column;
        align-items: stretch;
    }
    
    .bookinn-tab-links {
        overflow-x: auto;
        flex-wrap: nowrap;
    }
}

@media (max-width: 480px) {
    .bookinn-modal-content {
        min-width: 100vw;
        max-width: 100vw;
        height: 100vh;
        max-height: 100vh;
        margin: 0;
        border-radius: 0;
    }
    
    .bookinn-modal-header {
        border-radius: 0;
    }
}

/* ===== ACCESSIBILITY ===== */
.bookinn-modal[aria-hidden="true"] {
    display: none;
}

.bookinn-modal[aria-hidden="false"] {
    display: flex;
}

.bookinn-modal:focus {
    outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bookinn-modal {
        background-color: rgba(0, 0, 0, 0.8);
    }
    
    .bookinn-modal-content {
        border: 3px solid var(--bookinn-text-primary);
    }
    
    .bookinn-input,
    .bookinn-select,
    .bookinn-textarea {
        border-width: 2px;
    }
}

/* ===== LOADING INDICATORS - Migrated from frontend-dashboard ===== */
.bookinn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.bookinn-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid var(--bookinn-gray-200);
    border-top: 2px solid var(--bookinn-primary);
    border-radius: 50%;
    animation: bookinn-spin 1s linear infinite;
    z-index: 1000;
}

@keyframes bookinn-spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ===== SUCCESS/ERROR MESSAGES - Migrated from frontend-dashboard ===== */
.bookinn-success-message,
.bookinn-error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: var(--bookinn-border-radius-lg);
    box-shadow: var(--bookinn-shadow-lg);
    z-index: 10000;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    animation: bookinn-slide-in 0.3s ease-out;
}

.bookinn-success-message {
    background: var(--bookinn-success);
    color: white;
    border-left: 4px solid #059669;
}

.bookinn-error-message {
    background: var(--bookinn-error);
    color: white;
    border-left: 4px solid #dc2626;
}

.bookinn-success-message i,
.bookinn-error-message i {
    font-size: 16px;
    flex-shrink: 0;
}

@keyframes bookinn-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== CHART CONTAINERS - Migrated from frontend-dashboard ===== */
.bookinn-chart-wrapper {
    height: 350px;
    min-height: 350px;
    position: relative;
    background: white;
    border-radius: var(--bookinn-border-radius-lg);
    padding: 16px;
    box-shadow: var(--bookinn-shadow-sm);
}

.bookinn-chart-wrapper canvas {
    max-height: 100% !important;
}

/* ===== CALENDAR LOADING - Migrated from frontend-dashboard ===== */
.bookinn-calendar-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: var(--bookinn-border-radius-lg);
}

.bookinn-calendar-loading-content {
    text-align: center;
    color: var(--bookinn-gray-600);
}

.bookinn-calendar-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--bookinn-gray-200);
    border-top: 3px solid var(--bookinn-primary);
    border-radius: 50%;
    animation: bookinn-spin 1s linear infinite;
    margin: 0 auto 12px;
}

/* ===== CALENDAR PRIVACY - Hide Guest Names ===== */
.fc-event-title {
    /* Hide guest names, show only booking status */
    display: none;
}

.fc-event-title-container {
    /* Show only room number and status */
    font-weight: 600;
}

.fc-event-title-container::before {
    content: "Booked";
    font-size: 12px;
    font-weight: 500;
}

/* ===== ANIMATIONS ===== */
@keyframes bookinn-fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bookinn-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bookinn-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* ===== BOOKING WIZARD ADDITIONAL STYLES ===== */
.bookinn-wizard-body {
    padding: 32px !important;
    flex: 1 !important;
    overflow-y: auto !important;
}

.bookinn-wizard-step {
    display: none !important;
}

.bookinn-wizard-step.active {
    display: block !important;
}

.bookinn-wizard-nav {
    display: flex !important;
    justify-content: center !important;
    margin-bottom: 32px !important;
    padding: 0 !important;
    list-style: none !important;
}

.bookinn-wizard-nav-item {
    display: flex !important;
    align-items: center !important;
    padding: 12px 24px !important;
    margin: 0 8px !important;
    background-color: #f8fafc !important;
    color: #64748b !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-weight: 500 !important;
    border: 2px solid transparent !important;
}

.bookinn-wizard-nav-item:hover {
    background-color: #e2e8f0 !important;
    color: #475569 !important;
}

.bookinn-wizard-nav-item.active {
    background-color: #1e40af !important;
    color: white !important;
    border-color: #1e40af !important;
}

.bookinn-wizard-nav-item.completed {
    background-color: #059669 !important;
    color: white !important;
    border-color: #059669 !important;
}

.bookinn-room-selection {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
    gap: 20px !important;
    margin-top: 20px !important;
}

.bookinn-room-card {
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    padding: 20px !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    background-color: #ffffff !important;
}

.bookinn-room-card:hover {
    border-color: #3b82f6 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.bookinn-room-card.selected {
    border-color: #059669 !important;
    background-color: #f0fdf4 !important;
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2) !important;
}

.bookinn-room-card h4 {
    margin: 0 0 8px 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
}

.bookinn-room-card .room-type {
    color: #6b7280 !important;
    font-size: 14px !important;
    margin: 0 0 4px 0 !important;
}

.bookinn-room-card .room-capacity {
    color: #6b7280 !important;
    font-size: 12px !important;
    margin: 0 0 16px 0 !important;
}

.bookinn-room-card .room-price {
    display: flex !important;
    align-items: baseline !important;
    margin-bottom: 16px !important;
}

.bookinn-room-card .price {
    font-size: 24px !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    margin-right: 8px !important;
}

.bookinn-room-card .per-night {
    font-size: 12px !important;
    color: #6b7280 !important;
}

.bookinn-room-card .select-room {
    width: 100% !important;
    padding: 10px 16px !important;
    background-color: #3b82f6 !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: background-color 0.2s ease !important;
}

.bookinn-room-card .select-room:hover {
    background-color: #2563eb !important;
}

.bookinn-room-card.selected .select-room {
    background-color: #059669 !important;
}

.bookinn-room-card.selected .select-room:hover {
    background-color: #047857 !important;
}

.no-rooms {
    text-align: center !important;
    color: #6b7280 !important;
    font-style: italic !important;
    padding: 40px !important;
    background-color: #f9fafb !important;
    border-radius: 8px !important;
    border: 2px dashed #d1d5db !important;
}

/* Wizard footer */
.bookinn-wizard-footer {
    padding: 24px 32px !important;
    border-top: 1px solid #e2e8f0 !important;
    background-color: #f8fafc !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    border-radius: 0 0 12px 12px !important;
}

.bookinn-wizard-footer .bookinn-btn {
    padding: 12px 24px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.bookinn-wizard-footer .bookinn-btn-secondary {
    background-color: #6b7280 !important;
    color: white !important;
}

.bookinn-wizard-footer .bookinn-btn-secondary:hover {
    background-color: #4b5563 !important;
}

.bookinn-wizard-footer .bookinn-btn-primary {
    background-color: #1e40af !important;
    color: white !important;
}

.bookinn-wizard-footer .bookinn-btn-primary:hover {
    background-color: #1e3a8a !important;
}

/* Summary section styling */
.bookinn-booking-summary {
    background-color: #f0f9ff !important;
    border: 2px solid #bae6fd !important;
    border-radius: 12px !important;
    padding: 24px !important;
    margin-top: 24px !important;
}

.bookinn-booking-summary h5 {
    margin: 0 0 16px 0 !important;
    color: #0c4a6e !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

.bookinn-summary-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid #bae6fd !important;
}

.bookinn-summary-item:last-child {
    border-bottom: none !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    margin-top: 8px !important;
    padding-top: 16px !important;
    border-top: 2px solid #0ea5e9 !important;
}

.bookinn-summary-label {
    color: #0c4a6e !important;
    font-weight: 500 !important;
}

.bookinn-summary-value {
    color: #1e40af !important;
    font-weight: 600 !important;
}
