<?php
/**
 * BookInn Unified Database Manager
 * 
 * Centralizes all database operations and schema definitions
 * Replaces the chaotic dual-schema approach with a single, unified system
 */

if (!defined('ABSPATH')) {
    exit;
}

class BookInn_Database_Manager {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Database table names (unified schema)
     */
    private static $tables = array();
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        global $wpdb;
        
        // Determina il prefisso da usare
        $prefix = 'wpbi_'; // Default fallback
        if ($wpdb && isset($wpdb->prefix)) {
            $prefix = $wpdb->prefix;
        }
        
        // Initialize unified table names (bookinn_* schema only)
        self::$tables = array(
            'hotels'             => $prefix . 'bookinn_hotels',
            'room_types'         => $prefix . 'bookinn_room_types', 
            'rooms'              => $prefix . 'bookinn_rooms',
            'guests'             => $prefix . 'bookinn_guests',
            'bookings'           => $prefix . 'bookinn_bookings',
            'booking_services'   => $prefix . 'bookinn_booking_services',
            'payments'           => $prefix . 'bookinn_payments',
            'rate_plans'         => $prefix . 'bookinn_rate_plans',
            'availability'       => $prefix . 'bookinn_availability',
            'settings'           => $prefix . 'bookinn_settings',
            
            // OTA Tables
            'ota_channels'       => $prefix . 'bookinn_ota_channels',
            'ota_room_mapping'   => $prefix . 'bookinn_ota_room_mapping',
            'ota_sync_queue'     => $prefix . 'bookinn_ota_sync_queue',
            'ota_logs'           => $prefix . 'bookinn_ota_logs',
            'ota_rate_plans'     => $prefix . 'bookinn_ota_rate_plans',
        );
    }
    
    /**
     * Get table name by key
     */
    public static function get_table($table_key) {
        // Assicurati che il singleton sia inizializzato
        if (self::$instance === null) {
            self::get_instance();
        }
        
        if (!isset(self::$tables[$table_key])) {
            // Se wp_die non è disponibile, usa un'eccezione standard
            if (function_exists('wp_die')) {
                wp_die("Unknown table key: {$table_key}");
            } else {
                throw new Exception("Unknown table key: {$table_key}");
            }
        }
        return self::$tables[$table_key];
    }
    
    /**
     * Get all table names
     */
    public static function get_all_tables() {
        // Assicurati che il singleton sia inizializzato
        if (self::$instance === null) {
            self::get_instance();
        }
        return self::$tables;
    }
    
    /**
     * Create all database tables
     */
    public static function create_tables() {
        global $wpdb;
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $charset_collate = $wpdb->get_charset_collate();
        
        // === UNIFIED SCHEMA DEFINITIONS ===
        
        // Hotels table
        $sql_hotels = "CREATE TABLE " . self::get_table('hotels') . " (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            address text,
            phone varchar(50),
            email varchar(100),
            website varchar(255),
            check_in_time time DEFAULT '14:00:00',
            check_out_time time DEFAULT '11:00:00',
            currency varchar(3) DEFAULT 'EUR',
            tax_rate decimal(5,2) DEFAULT 0.00,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        // Room types table
        $sql_room_types = "CREATE TABLE " . self::get_table('room_types') . " (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            hotel_id mediumint(9) NOT NULL,
            name varchar(255) NOT NULL,
            description text,
            max_adults tinyint(2) NOT NULL DEFAULT 2,
            max_children tinyint(2) NOT NULL DEFAULT 0,
            max_guests tinyint(2) NOT NULL DEFAULT 2,
            base_price decimal(10,2) NOT NULL DEFAULT 0.00,
            amenities text,
            images text,
            is_active tinyint(1) NOT NULL DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY hotel_id (hotel_id)
        ) $charset_collate;";
        
        // Rooms table
        $sql_rooms = "CREATE TABLE " . self::get_table('rooms') . " (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            hotel_id mediumint(9) NOT NULL,
            room_type_id mediumint(9) NOT NULL,
            room_number varchar(50) NOT NULL,
            name varchar(255),
            floor tinyint(2),
            status enum('available', 'occupied', 'maintenance', 'out_of_order') DEFAULT 'available',
            is_active tinyint(1) NOT NULL DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY hotel_id (hotel_id),
            KEY room_type_id (room_type_id),
            KEY status (status),
            UNIQUE KEY unique_room (hotel_id, room_number)
        ) $charset_collate;";
        
        // Guests table
        $sql_guests = "CREATE TABLE " . self::get_table('guests') . " (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            hotel_id int(11) NULL,
            first_name varchar(100) NOT NULL,
            last_name varchar(100) NOT NULL,
            email varchar(100) NOT NULL,
            phone varchar(50),
            address text,
            city varchar(100),
            country varchar(100),
            postal_code varchar(20),
            id_type enum('passport', 'id_card', 'driver_license', 'other'),
            id_number varchar(100),
            date_of_birth date,
            nationality varchar(100),
            notes text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY email (email)
        ) $charset_collate;";
        
        // Bookings table  
        $sql_bookings = "CREATE TABLE " . self::get_table('bookings') . " (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            hotel_id mediumint(9) NOT NULL,
            guest_id mediumint(9) NOT NULL,
            room_id mediumint(9) NOT NULL,
            booking_reference varchar(50) UNIQUE,
            check_in_date date NOT NULL,
            check_out_date date NOT NULL,
            adults tinyint(2) NOT NULL DEFAULT 1,
            children tinyint(2) NOT NULL DEFAULT 0,
            status enum('pending', 'confirmed', 'checked_in', 'checked_out', 'cancelled', 'no_show') DEFAULT 'pending',
            booking_source enum('direct', 'website', 'phone', 'email', 'walk_in', 'booking_com', 'airbnb', 'other') DEFAULT 'direct',
            subtotal decimal(10,2) NOT NULL DEFAULT 0.00,
            tax_amount decimal(10,2) NOT NULL DEFAULT 0.00,
            total_amount decimal(10,2) NOT NULL DEFAULT 0.00,
            paid_amount decimal(10,2) NOT NULL DEFAULT 0.00,
            payment_status enum('pending', 'partial', 'paid', 'refunded') DEFAULT 'pending',
            special_requests text,
            internal_notes text,
            check_in_time datetime,
            check_out_time datetime,
            created_by bigint(20) UNSIGNED,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY hotel_id (hotel_id),
            KEY guest_id (guest_id),
            KEY room_id (room_id),
            KEY check_in_date (check_in_date),
            KEY check_out_date (check_out_date),
            KEY status (status)
        ) $charset_collate;";
        
        // Execute table creation
        dbDelta($sql_hotels);
        dbDelta($sql_room_types);
        dbDelta($sql_rooms);
        dbDelta($sql_guests);
        dbDelta($sql_bookings);
        
        // Create OTA tables if class exists
        if (class_exists('BookInn_OTA_Database_Schema')) {
            BookInn_OTA_Database_Schema::create_tables();
        }
        
        // Initialize default data if needed
        self::initialize_default_data();
    }
    
    /**
     * Check if table exists
     */
    public static function table_exists($table_key) {
        global $wpdb;
        $table_name = self::get_table($table_key);
        return $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    }
    
    /**
     * Get safe query with automatic table name resolution
     */
    public static function prepare_query($query, ...$args) {
        global $wpdb;
        
        // Assicurati che il singleton sia inizializzato
        if (self::$instance === null) {
            self::get_instance();
        }
        
        // Replace table placeholders {table_name} with actual table names
        foreach (self::$tables as $key => $table_name) {
            $query = str_replace("{{$key}}", $table_name, $query);
        }
        
        if (!empty($args)) {
            return $wpdb->prepare($query, ...$args);
        }
        return $query;
    }
    
    /**
     * Execute safe query
     */
    public static function query($query, ...$args) {
        global $wpdb;
        return $wpdb->query(self::prepare_query($query, ...$args));
    }
    
    /**
     * Get results with table name resolution
     */
    public static function get_results($query, ...$args) {
        global $wpdb;
        return $wpdb->get_results(self::prepare_query($query, ...$args));
    }
    
    /**
     * Get single row with table name resolution  
     */
    public static function get_row($query, ...$args) {
        global $wpdb;
        return $wpdb->get_row(self::prepare_query($query, ...$args));
    }
    
    /**
     * Get single value with table name resolution
     */
    public static function get_var($query, ...$args) {
        global $wpdb;
        return $wpdb->get_var(self::prepare_query($query, ...$args));
    }
    
    /**
     * Insert data safely
     */
    public static function insert($table_key, $data, $format = null) {
        global $wpdb;
        return $wpdb->insert(self::get_table($table_key), $data, $format);
    }
    
    /**
     * Update data safely
     */
    public static function update($table_key, $data, $where, $format = null, $where_format = null) {
        global $wpdb;
        return $wpdb->update(self::get_table($table_key), $data, $where, $format, $where_format);
    }
    
    /**
     * Delete data safely
     */
    public static function delete($table_key, $where, $where_format = null) {
        global $wpdb;
        return $wpdb->delete(self::get_table($table_key), $where, $where_format);
    }
    
    /**
     * Initialize default data
     */
    private static function initialize_default_data() {
        global $wpdb;
        
        // Check if hotel exists
        $hotel_exists = $wpdb->get_var("SELECT COUNT(*) FROM " . self::get_table('hotels'));
        
        if (!$hotel_exists) {
            // Insert default hotel
            self::insert('hotels', array(
                'name' => 'BookInn Demo Hotel',
                'description' => 'Default hotel for BookInn plugin',
                'address' => '123 Hotel Street, City',
                'phone' => '+1234567890',
                'email' => '<EMAIL>',
                'currency' => 'EUR',
                'tax_rate' => 10.00
            ));
        }
    }
    
    /**
     * Drop obsolete tables
     */
    public static function cleanup_obsolete_tables() {
        global $wpdb;
        
        $obsolete_tables = array(
            'booking_hotels',
            'booking_rooms', 
            'booking_reservations',
            'booking_availability',
            'booking_ota_mappings',
            'booking_pricing_rules',
            'booking_services',
            'booking_reservation_services'
        );
        
        foreach ($obsolete_tables as $table) {
            $full_table_name = $wpdb->prefix . $table;
            $wpdb->query("DROP TABLE IF EXISTS $full_table_name");
        }
    }
    
    /**
     * Get database schema version
     */
    public static function get_schema_version() {
        return get_option('bookinn_db_version', '1.0.0');
    }
    
    /**
     * Update database schema version
     */
    public static function update_schema_version($version) {
        update_option('bookinn_db_version', $version);
    }
}
