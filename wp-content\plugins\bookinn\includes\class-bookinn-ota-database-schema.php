<?php
/**
 * BookInn OTA Database Schema
 * Creates OTA-specific tables for the OTA Management widget
 */

if (!defined('ABSPATH')) {
    exit;
}

class BookInn_OTA_Database_Schema {
    
    /**
     * Create OTA-specific tables
     */
    public static function create_tables() {
        global $wpdb;
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // OTA Channels table
        $table_ota_channels = $wpdb->prefix . 'bookinn_ota_channels';
        $sql_ota_channels = "CREATE TABLE $table_ota_channels (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            channel_name varchar(100) NOT NULL,
            channel_type enum('booking_com', 'airbnb', 'expedia', 'agoda', 'hotels_com', 'other') NOT NULL,
            api_key varchar(255),
            api_secret varchar(255),
            property_id varchar(100),
            status enum('active', 'inactive', 'error') DEFAULT 'inactive',
            sync_status enum('pending', 'success', 'failed') DEFAULT 'pending',
            last_sync_at datetime,
            sync_frequency enum('hourly', 'daily', 'weekly') DEFAULT 'daily',
            auto_sync tinyint(1) DEFAULT 1,
            settings longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY channel_type (channel_type),
            KEY status (status),
            KEY sync_status (sync_status)
        ) $charset_collate;";
        
        // OTA Room Mapping table
        $table_ota_room_mapping = $wpdb->prefix . 'bookinn_ota_room_mapping';
        $sql_ota_room_mapping = "CREATE TABLE $table_ota_room_mapping (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            local_room_id mediumint(9) NOT NULL,
            ota_channel_id mediumint(9) NOT NULL,
            remote_room_id varchar(100) NOT NULL,
            remote_room_name varchar(255),
            status enum('active', 'inactive', 'error') DEFAULT 'active',
            price_multiplier decimal(5,2) DEFAULT 1.00,
            availability_sync tinyint(1) DEFAULT 1,
            price_sync tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY local_room_id (local_room_id),
            KEY ota_channel_id (ota_channel_id),
            KEY status (status),
            UNIQUE KEY unique_mapping (local_room_id, ota_channel_id)
        ) $charset_collate;";
        
        // OTA Sync Queue table
        $table_ota_sync_queue = $wpdb->prefix . 'bookinn_ota_sync_queue';
        $sql_ota_sync_queue = "CREATE TABLE $table_ota_sync_queue (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            ota_channel_id mediumint(9) NOT NULL,
            sync_type enum('availability', 'rates', 'inventory', 'bookings', 'restrictions') NOT NULL,
            room_id mediumint(9),
            date_from date,
            date_to date,
            priority tinyint(2) DEFAULT 5,
            status enum('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            attempts tinyint(2) DEFAULT 0,
            max_attempts tinyint(2) DEFAULT 3,
            error_message text,
            payload longtext,
            scheduled_at datetime DEFAULT CURRENT_TIMESTAMP,
            processed_at datetime,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY ota_channel_id (ota_channel_id),
            KEY sync_type (sync_type),
            KEY status (status),
            KEY scheduled_at (scheduled_at),
            KEY priority (priority)
        ) $charset_collate;";
        
        // OTA Activity Logs table
        $table_ota_logs = $wpdb->prefix . 'bookinn_ota_logs';
        $sql_ota_logs = "CREATE TABLE $table_ota_logs (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            ota_channel_id mediumint(9),
            action varchar(100) NOT NULL,
            action_type enum('sync', 'booking', 'error', 'config', 'mapping') NOT NULL,
            status enum('success', 'failed', 'warning', 'info') NOT NULL,
            message text,
            details longtext,
            room_id mediumint(9),
            booking_id mediumint(9),
            user_id bigint(20) UNSIGNED,
            ip_address varchar(45),
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY ota_channel_id (ota_channel_id),
            KEY action_type (action_type),
            KEY status (status),
            KEY created_at (created_at),
            KEY room_id (room_id),
            KEY booking_id (booking_id)
        ) $charset_collate;";
        
        // OTA Rate Plans table
        $table_ota_rate_plans = $wpdb->prefix . 'bookinn_ota_rate_plans';
        $sql_ota_rate_plans = "CREATE TABLE $table_ota_rate_plans (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            ota_channel_id mediumint(9) NOT NULL,
            room_mapping_id mediumint(9) NOT NULL,
            remote_rate_plan_id varchar(100) NOT NULL,
            rate_plan_name varchar(255),
            base_rate decimal(10,2) DEFAULT 0.00,
            currency varchar(3) DEFAULT 'EUR',
            date_from date NOT NULL,
            date_to date NOT NULL,
            min_stay tinyint(2) DEFAULT 1,
            max_stay tinyint(2),
            closed_to_arrival tinyint(1) DEFAULT 0,
            closed_to_departure tinyint(1) DEFAULT 0,
            stop_sell tinyint(1) DEFAULT 0,
            status enum('active', 'inactive') DEFAULT 'active',
            last_updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY ota_channel_id (ota_channel_id),
            KEY room_mapping_id (room_mapping_id),
            KEY date_from (date_from),
            KEY date_to (date_to),
            KEY status (status),
            UNIQUE KEY unique_rate_plan (ota_channel_id, room_mapping_id, remote_rate_plan_id, date_from)
        ) $charset_collate;";
        
        // Execute table creation
        dbDelta($sql_ota_channels);
        dbDelta($sql_ota_room_mapping);
        dbDelta($sql_ota_sync_queue);
        dbDelta($sql_ota_logs);
        dbDelta($sql_ota_rate_plans);
        
        // Insert sample OTA channels
        self::insert_sample_ota_data();
    }
    
    /**
     * Insert sample OTA data
     */
    private static function insert_sample_ota_data() {
        global $wpdb;
        
        $sample_channels = array(
            array(
                'channel_name' => 'Booking.com',
                'channel_type' => 'booking_com',
                'status' => 'inactive',
                'sync_frequency' => 'hourly'
            ),
            array(
                'channel_name' => 'Airbnb',
                'channel_type' => 'airbnb',
                'status' => 'inactive',
                'sync_frequency' => 'daily'
            ),
            array(
                'channel_name' => 'Expedia',
                'channel_type' => 'expedia',
                'status' => 'inactive',
                'sync_frequency' => 'daily'
            )
        );
        
        foreach ($sample_channels as $channel) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$wpdb->prefix}bookinn_ota_channels WHERE channel_type = %s",
                $channel['channel_type']
            ));
            
            if (!$existing) {
                $wpdb->insert(
                    $wpdb->prefix . 'bookinn_ota_channels',
                    $channel,
                    array('%s', '%s', '%s', '%s')
                );
            }
        }
        
        // Insert sample log entry
        $wpdb->insert(
            $wpdb->prefix . 'bookinn_ota_logs',
            array(
                'action' => 'system_init',
                'action_type' => 'config',
                'status' => 'success',
                'message' => 'OTA Management system initialized successfully',
                'user_id' => get_current_user_id()
            ),
            array('%s', '%s', '%s', '%s', '%d')
        );
    }
}
