<?php
/**
 * BookInn Models
 * Handles database operations for the booking system
 */

if (!defined('ABSPATH')) {
    exit;
}

class BookInn_Models {

    /**
     * Restituisce i dettagli di una singola prenotazione (inclusi i dati guest)
     */
    public static function get_booking_by_id($booking_id) {
        global $wpdb;

        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        $table_guests = $wpdb->prefix . 'bookinn_guests';
        $table_rooms = $wpdb->prefix . 'bookinn_rooms';

        $sql = $wpdb->prepare(
            "SELECT b.*, 
                    g.first_name, g.last_name, g.email, g.phone,
                    r.name as room_name, r.room_number
             FROM $table_bookings b
             LEFT JOIN $table_guests g ON b.guest_id = g.id
             LEFT JOIN $table_rooms r ON b.room_id = r.id
             WHERE b.id = %d
             LIMIT 1",
            $booking_id
        );

        $booking = $wpdb->get_row($sql);

        if (!$booking) {
            return null;
        }

        // Formatta i dati guest e booking
        return array(
            'id' => $booking->id,
            'guest_name' => trim($booking->first_name . ' ' . $booking->last_name),
            'guest_first_name' => $booking->first_name,
            'guest_last_name' => $booking->last_name,
            'guest_email' => $booking->email,
            'guest_phone' => $booking->phone,
            'guest_address' => isset($booking->address) ? $booking->address : '',
            'room_name' => $booking->room_name ?: $booking->room_number,
            'room_number' => $booking->room_number,
            'check_in' => $booking->check_in_date,
            'check_out' => $booking->check_out_date,
            'status' => $booking->status,
            'total_amount' => $booking->total_amount,
            // altri campi utili per il frontend
        );
    }
    
    /**
     * Get booking statistics
     */
    public static function get_booking_statistics() {
        global $wpdb;
        
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        
        $today = date('Y-m-d');
        
        // Total bookings
        $total_bookings = $wpdb->get_var("SELECT COUNT(*) FROM $table_bookings WHERE status != 'cancelled'");
        
        // Today's check-ins
        $todays_checkins = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_bookings WHERE check_in_date = %s AND status IN ('confirmed', 'checked_in')",
            $today
        ));
        
        // Today's check-outs
        $todays_checkouts = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_bookings WHERE check_out_date = %s AND status = 'checked_in'",
            $today
        ));
        
        // Pending bookings
        $pending_bookings = $wpdb->get_var("SELECT COUNT(*) FROM $table_bookings WHERE status = 'pending'");
        
        // Confirmed bookings
        $confirmed_bookings = $wpdb->get_var("SELECT COUNT(*) FROM $table_bookings WHERE status = 'confirmed'");
        
        return array(
            'total_bookings' => intval($total_bookings),
            'todays_checkins' => intval($todays_checkins),
            'todays_checkouts' => intval($todays_checkouts),
            'pending_bookings' => intval($pending_bookings),
            'confirmed_bookings' => intval($confirmed_bookings)
        );
    }
    
    /**
     * Get current occupancy rate
     */
    public static function get_current_occupancy() {
        global $wpdb;
        
        $table_rooms = $wpdb->prefix . 'bookinn_rooms';
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        
        $today = date('Y-m-d');
        
        // Total active rooms
        $total_rooms = $wpdb->get_var("SELECT COUNT(*) FROM $table_rooms WHERE status = 'active'");
        
        // Occupied rooms today
        $occupied_rooms = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT room_id) FROM $table_bookings 
             WHERE %s BETWEEN check_in_date AND check_out_date 
             AND status IN ('confirmed', 'checked_in')",
            $today
        ));
        
        if ($total_rooms == 0) {
            return 0;
        }
        
        return round(($occupied_rooms / $total_rooms) * 100, 1);
    }
    
    /**
     * Get recent bookings for display
     */
    public static function get_recent_bookings($limit = 10) {
        global $wpdb;
        
        $bookings = $wpdb->get_results($wpdb->prepare("
            SELECT b.*, g.first_name, g.last_name, g.email,
                   r.name as room_name,
                   h.name as hotel_name
            FROM {$wpdb->prefix}bookinn_bookings b
            LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
            LEFT JOIN {$wpdb->prefix}bookinn_rooms r ON b.room_id = r.id
            LEFT JOIN {$wpdb->prefix}bookinn_hotels h ON r.hotel_id = h.id
            ORDER BY b.created_at DESC
            LIMIT %d
        ", $limit), ARRAY_A);
        
        foreach ($bookings as &$booking) {
            $booking['guest_name'] = trim($booking['first_name'] . ' ' . $booking['last_name']);
            $booking['total_formatted'] = '€' . number_format($booking['total_amount'], 2);
            
            // Status labels
            $status_labels = array(
                'pending' => __('Pending', 'bookinn'),
                'confirmed' => __('Confirmed', 'bookinn'),
                'checked_in' => __('Checked In', 'bookinn'),
                'checked_out' => __('Checked Out', 'bookinn'),
                'cancelled' => __('Cancelled', 'bookinn'),
                'no_show' => __('No Show', 'bookinn')
            );
            $booking['status_label'] = $status_labels[$booking['status']] ?? $booking['status'];
        }
        
        return $bookings;
    }
    
    /**
     * Get upcoming check-ins
     */
    public static function get_upcoming_checkins($limit = 5) {
        global $wpdb;
        
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        $table_guests = $wpdb->prefix . 'bookinn_guests';
        $table_rooms = $wpdb->prefix . 'bookinn_rooms';
        
        $today = date('Y-m-d');
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT b.id, b.check_in_date,
                    CONCAT(g.first_name, ' ', g.last_name) as guest_name,
                    r.name as room_name, r.room_number
             FROM $table_bookings b
             LEFT JOIN $table_guests g ON b.guest_id = g.id
             LEFT JOIN $table_rooms r ON b.room_id = r.id
             WHERE b.check_in_date = %s 
             AND b.status = 'confirmed'
             ORDER BY b.check_in_date ASC
             LIMIT %d",
            $today,
            $limit
        ));
        
        $checkins = array();
        
        foreach ($results as $checkin) {
            $checkins[] = array(
                'id' => $checkin->id,
                'guest_name' => $checkin->guest_name ?: __('Unknown Guest', 'bookinn'),
                'check_in' => date('d/m/Y', strtotime($checkin->check_in_date)),
                'room_name' => $checkin->room_name ?: $checkin->room_number ?: __('Unknown Room', 'bookinn')
            );
        }
        
        return $checkins;
    }
    
    /**
     * Get all bookings
     */
    public static function get_all_bookings($filters = array()) {
        global $wpdb;
        
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        $table_guests = $wpdb->prefix . 'bookinn_guests';
        $table_rooms = $wpdb->prefix . 'bookinn_rooms';
        
        $where_clauses = array();
        $params = array();
        
        // Apply filters
        if (!empty($filters['status'])) {
            $where_clauses[] = "b.status = %s";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['date_from'])) {
            $where_clauses[] = "b.check_in_date >= %s";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where_clauses[] = "b.check_out_date <= %s";
            $params[] = $filters['date_to'];
        }
        
        $where_sql = '';
        if (!empty($where_clauses)) {
            $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        }
        
        $sql = "SELECT b.id, b.check_in_date, b.check_out_date, b.status, b.total_amount,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name,
                       g.email as guest_email,
                       COALESCE(r.name, r.room_number) as room_name
                FROM $table_bookings b
                LEFT JOIN $table_guests g ON b.guest_id = g.id
                LEFT JOIN $table_rooms r ON b.room_id = r.id
                $where_sql
                ORDER BY b.check_in_date DESC
                LIMIT 50";
        
        if (!empty($params)) {
            $results = $wpdb->get_results($wpdb->prepare($sql, $params));
        } else {
            $results = $wpdb->get_results($sql);
        }
        
        $bookings = array();
        
        foreach ($results as $booking) {
            $bookings[] = array(
                'id' => $booking->id,
                'guest_name' => $booking->guest_name ?: __('Unknown Guest', 'bookinn'),
                'guest_email' => $booking->guest_email ?: '',
                'check_in' => date('d/m/Y', strtotime($booking->check_in_date)),
                'check_out' => date('d/m/Y', strtotime($booking->check_out_date)),
                'room_name' => $booking->room_name ?: __('Unknown Room', 'bookinn'),
                'status' => $booking->status,
                'status_label' => self::get_status_label($booking->status),
                'total_formatted' => self::format_currency($booking->total_amount)
            );
        }
        
        return $bookings;
    }
    
    /**
     * Get all rooms
     */
    public static function get_all_rooms() {
        global $wpdb;
        
        $table_rooms = $wpdb->prefix . 'bookinn_rooms';
        $table_room_types = $wpdb->prefix . 'bookinn_room_types';
        
        $results = $wpdb->get_results(
            "SELECT r.id, r.room_number, r.name, r.status,
                    rt.name as type_name, rt.max_guests, rt.base_price
             FROM $table_rooms r
             LEFT JOIN $table_room_types rt ON r.room_type_id = rt.id
             WHERE r.is_active = 1
             ORDER BY r.room_number ASC"
        );
        
        $rooms = array();
        
        foreach ($results as $room) {
            $rooms[] = array(
                'id' => $room->id,
                'name' => $room->name ?: $room->room_number,
                'type' => $room->type_name ?: __('Standard', 'bookinn'),
                'max_guests' => intval($room->max_guests),
                'price_formatted' => self::format_currency($room->base_price),
                'status' => $room->status,
                'status_label' => self::get_room_status_label($room->status)
            );
        }
        
        return $rooms;
    }
    
    /**
     * Get available rooms
     */
    public static function get_available_rooms($check_in = null, $check_out = null) {
        global $wpdb;
        
        $table_rooms = $wpdb->prefix . 'bookinn_rooms';
        $table_room_types = $wpdb->prefix . 'bookinn_room_types';
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        
        $where_clauses = array("r.is_active = 1", "r.status = 'available'");
        $params = array();
        
        // Check availability for specific dates
        if ($check_in && $check_out) {
            $where_clauses[] = "r.id NOT IN (
                SELECT room_id FROM $table_bookings 
                WHERE (check_in_date < %s AND check_out_date > %s)
                AND status IN ('confirmed', 'checked_in')
            )";
            $params[] = $check_out;
            $params[] = $check_in;
        }
        
        $where_sql = implode(' AND ', $where_clauses);
        
        $sql = "SELECT r.id, r.room_number, r.name,
                       rt.name as type_name, rt.base_price
                FROM $table_rooms r
                LEFT JOIN $table_room_types rt ON r.room_type_id = rt.id
                WHERE $where_sql
                ORDER BY rt.base_price ASC, r.room_number ASC";
        
        if (!empty($params)) {
            $results = $wpdb->get_results($wpdb->prepare($sql, $params));
        } else {
            $results = $wpdb->get_results($sql);
        }
        
        $rooms = array();
        
        foreach ($results as $room) {
            $rooms[] = array(
                'id' => $room->id,
                'name' => $room->name ?: $room->room_number,
                'price_formatted' => self::format_currency($room->base_price)
            );
        }
        
        return $rooms;
    }
    
    /**
     * Get reports data
     */
    public static function get_reports_data($period = 'last_30_days', $date_from = null, $date_to = null) {
        global $wpdb;
        
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        
        // Calculate date range
        switch ($period) {
            case 'this_month':
                $date_from = date('Y-m-01');
                $date_to = date('Y-m-t');
                break;
            case 'last_month':
                $date_from = date('Y-m-01', strtotime('last month'));
                $date_to = date('Y-m-t', strtotime('last month'));
                break;
            case 'this_year':
                $date_from = date('Y-01-01');
                $date_to = date('Y-12-31');
                break;
            case 'last_30_days':
            default:
                $date_from = date('Y-m-d', strtotime('-30 days'));
                $date_to = date('Y-m-d');
                break;
        }
        
        // Total revenue
        $total_revenue = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(total_amount) FROM $table_bookings 
             WHERE check_in_date BETWEEN %s AND %s 
             AND status != 'cancelled'",
            $date_from, $date_to
        ));
        
        // Total bookings
        $total_bookings = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_bookings 
             WHERE check_in_date BETWEEN %s AND %s 
             AND status != 'cancelled'",
            $date_from, $date_to
        ));
        
        // Average stay
        $average_stay = $wpdb->get_var($wpdb->prepare(
            "SELECT AVG(DATEDIFF(check_out_date, check_in_date)) FROM $table_bookings 
             WHERE check_in_date BETWEEN %s AND %s 
             AND status != 'cancelled'",
            $date_from, $date_to
        ));
        
        $occupancy_rate = self::get_current_occupancy();
        
        return array(
            'total_revenue' => self::format_currency($total_revenue ?: 0),
            'total_bookings' => intval($total_bookings),
            'average_stay' => number_format($average_stay ?: 0, 1),
            'occupancy_rate' => $occupancy_rate
        );
    }
    
    /**
     * Create a new booking
     */
    public static function create_booking($booking_data) {
        global $wpdb;
        
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        $table_guests = $wpdb->prefix . 'bookinn_guests';
        
        try {
            $wpdb->query('START TRANSACTION');
            
            // Create or get guest
            $guest_id = self::create_or_get_guest($booking_data['guest']);
            
            if (!$guest_id) {
                throw new Exception(__('Failed to create guest record', 'bookinn'));
            }
            
            // Generate booking reference
            $booking_reference = self::generate_booking_reference();
            
            // Calculate total amount
            $total_amount = self::calculate_booking_total($booking_data);
            
            // Insert booking
            $booking_insert = $wpdb->insert(
                $table_bookings,
                array(
                    'hotel_id' => 1, // Default hotel
                    'guest_id' => $guest_id,
                    'room_id' => $booking_data['room_id'],
                    'booking_reference' => $booking_reference,
                    'check_in_date' => $booking_data['check_in'],
                    'check_out_date' => $booking_data['check_out'],
                    'adults' => $booking_data['adults'],
                    'children' => $booking_data['children'],
                    'status' => 'confirmed',
                    'booking_source' => 'direct',
                    'subtotal' => $total_amount,
                    'total_amount' => $total_amount,
                    'special_requests' => $booking_data['notes'],
                    'created_by' => get_current_user_id()
                ),
                array('%d', '%d', '%d', '%s', '%s', '%s', '%d', '%d', '%s', '%s', '%f', '%f', '%s', '%d')
            );
            
            if ($booking_insert === false) {
                throw new Exception(__('Failed to create booking', 'bookinn'));
            }
            
            $booking_id = $wpdb->insert_id;
            
            $wpdb->query('COMMIT');
            
            return $booking_id;
            
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }
    
    /**
     * Create or get guest
     */
    private static function create_or_get_guest($guest_data) {
        global $wpdb;
        
        $table_guests = $wpdb->prefix . 'bookinn_guests';
        
        // Check if guest exists by email
        $existing_guest = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_guests WHERE email = %s",
            $guest_data['email']
        ));
        
        if ($existing_guest) {
            return $existing_guest;
        }
        
        // Parse full name
        $name_parts = explode(' ', $guest_data['name'], 2);
        $first_name = $name_parts[0];
        $last_name = isset($name_parts[1]) ? $name_parts[1] : '';
        
        // Create new guest
        $insert_result = $wpdb->insert(
            $table_guests,
            array(
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $guest_data['email'],
                'phone' => $guest_data['phone'] ?: ''
            ),
            array('%s', '%s', '%s', '%s')
        );
        
        if ($insert_result === false) {
            return false;
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Generate booking reference
     */
    private static function generate_booking_reference() {
        return 'BK' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
    }
    
    /**
     * Calculate booking total
     */
    private static function calculate_booking_total($booking_data) {
        global $wpdb;
        
        $table_room_types = $wpdb->prefix . 'bookinn_room_types';
        $table_rooms = $wpdb->prefix . 'bookinn_rooms';
        
        // Get room price
        $room_price = $wpdb->get_var($wpdb->prepare(
            "SELECT rt.base_price 
             FROM $table_rooms r
             LEFT JOIN $table_room_types rt ON r.room_type_id = rt.id
             WHERE r.id = %d",
            $booking_data['room_id']
        ));
        
        if (!$room_price) {
            return 0;
        }
        
        // Calculate nights
        $check_in = new DateTime($booking_data['check_in']);
        $check_out = new DateTime($booking_data['check_out']);
        $nights = $check_in->diff($check_out)->days;
        
        return $room_price * $nights;
    }
    
    /**
     * Get status label
     */
    private static function get_status_label($status) {
        $labels = array(
            'pending' => __('Pending', 'bookinn'),
            'confirmed' => __('Confirmed', 'bookinn'),
            'checked_in' => __('Checked In', 'bookinn'),
            'checked_out' => __('Checked Out', 'bookinn'),
            'cancelled' => __('Cancelled', 'bookinn'),
            'no_show' => __('No Show', 'bookinn')
        );
        
        return isset($labels[$status]) ? $labels[$status] : $status;
    }
    
    /**
     * Get room status label
     */
    private static function get_room_status_label($status) {
        $labels = array(
            'available' => __('Available', 'bookinn'),
            'occupied' => __('Occupied', 'bookinn'),
            'maintenance' => __('Maintenance', 'bookinn'),
            'out_of_order' => __('Out of Order', 'bookinn')
        );
        
        return isset($labels[$status]) ? $labels[$status] : $status;
    }
    
    /**
     * Format currency
     */
    private static function format_currency($amount) {
        $currency = get_option('bookinn_settings', array())['currency'] ?? 'EUR';
        $symbol = '€'; // Default to Euro, could be expanded
        
        return $symbol . number_format($amount, 2);
    }
    
    /**
     * Get room types
     */
    public static function get_room_types($active_only = true) {
        global $wpdb;
        
        $table_rooms = $wpdb->prefix . 'booking_rooms';
        
        $where_clause = '';
        if ($active_only) {
            $where_clause = "WHERE status = 'active' AND room_type IS NOT NULL AND room_type != ''";
        }
        
        $results = $wpdb->get_results("
            SELECT DISTINCT room_type as name, room_type, base_price, capacity as max_guests
            FROM $table_rooms 
            $where_clause
            ORDER BY room_type
        ");
        
        return $results ?: array();
    }
    
    /**
     * Get rooms by status or all rooms
     */
    public static function get_rooms($filters = array()) {
        global $wpdb;
        
        $table_rooms = $wpdb->prefix . 'booking_rooms';
        
        $where_clauses = array("status = 'active'");
        $params = array();
        
        if (!empty($filters['status'])) {
            $where_clauses = array('status = %s');
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['room_type'])) {
            $where_clauses[] = 'room_type = %s';
            $params[] = $filters['room_type'];
        }
        
        $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        
        $sql = "SELECT *, room_type as room_type_name, capacity as max_guests
                FROM $table_rooms
                $where_sql
                ORDER BY room_number";
        
        if (!empty($params)) {
            $results = $wpdb->get_results($wpdb->prepare($sql, $params));
        } else {
            $results = $wpdb->get_results($sql);
        }
        
        return $results ?: array();
    }
    
    /**
     * Get calendar events for FullCalendar
     */
    public static function get_calendar_events($start_date = null, $end_date = null) {
        global $wpdb;
        
        $table_reservations = $wpdb->prefix . 'booking_reservations';
        $table_rooms = $wpdb->prefix . 'booking_rooms';
        
        $where_clauses = array("r.status IN ('confirmed', 'pending', 'checked_in')");
        $params = array();
        
        if ($start_date) {
            $where_clauses[] = 'r.check_out >= %s';
            $params[] = $start_date;
        }
        
        if ($end_date) {
            $where_clauses[] = 'r.check_in <= %s';
            $params[] = $end_date;
        }
        
        $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        
        $sql = "SELECT r.id, r.check_in as check_in_date, r.check_out as check_out_date, 
                       r.status, r.adults, r.children, r.guest_name,
                       rm.room_number, rm.name as room_name
                FROM $table_reservations r
                LEFT JOIN $table_rooms rm ON r.room_id = rm.id
                $where_sql
                ORDER BY r.check_in";
        
        if (!empty($params)) {
            $bookings = $wpdb->get_results($wpdb->prepare($sql, $params));
        } else {
            $bookings = $wpdb->get_results($sql);
        }
        
        $events = array();
        foreach ($bookings as $booking) {
            $color = '#3498db'; // Default blue
            switch ($booking->status) {
                case 'confirmed':
                    $color = '#27ae60'; // Green
                    break;
                case 'pending':
                    $color = '#f39c12'; // Orange
                    break;
                case 'checked_in':
                    $color = '#e74c3c'; // Red
                    break;
            }
            
            $events[] = array(
                'id' => $booking->id,
                'title' => $booking->guest_name . ' - ' . $booking->room_number,
                'start' => $booking->check_in_date,
                'end' => $booking->check_out_date,
                'color' => $color,
                'extendedProps' => array(
                    'booking_id' => $booking->id,
                    'guest_name' => $booking->guest_name,
                    'room_number' => $booking->room_number,
                    'room_name' => $booking->room_name,
                    'status' => $booking->status,
                    'adults' => $booking->adults,
                    'children' => $booking->children
                )
            );
        }
        
        return $events;
    }
}
