<?php
/**
 * BookInn Widget AJAX Handlers
 * Handles AJAX requests for the management widget
 */

if (!defined('ABSPATH')) {
    exit;
}

class BookInn_Widget_Ajax_Handler {
    /**
     * Restituisce i dettagli di una singola prenotazione (inclusi i dati guest)
     */
    public function get_booking_details() {
        check_ajax_referer('bookinn_ajax', 'nonce');

        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }

        $booking_id = intval($_POST['booking_id'] ?? 0);
        if (!$booking_id) {
            wp_send_json_error(array('message' => __('Invalid booking ID.', 'bookinn')));
        }

        // Recupera la prenotazione con JOIN su guest
        if (method_exists('BookInn_Models', 'get_booking_by_id')) {
            $booking = BookInn_Models::get_booking_by_id($booking_id);
        } else {
            $booking = null;
        }

        if ($booking) {
            wp_send_json_success($booking);
        } else {
            wp_send_json_error(array('message' => __('Booking not found.', 'bookinn')));
        }
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Dashboard content
        add_action('wp_ajax_bookinn_load_dashboard_content', array($this, 'load_dashboard_content'));
        
        // Bookings content
        add_action('wp_ajax_bookinn_load_bookings_content', array($this, 'load_bookings_content'));
        
        // Rooms content
        add_action('wp_ajax_bookinn_load_rooms_content', array($this, 'load_rooms_content'));
        
        // Calendar content
        add_action('wp_ajax_bookinn_load_calendar_content', array($this, 'load_calendar_content'));
        
        // Reports content
        add_action('wp_ajax_bookinn_load_reports_content', array($this, 'load_reports_content'));
        add_action('wp_ajax_bookinn_load_reports_overview', array($this, 'load_reports_overview'));
        add_action('wp_ajax_bookinn_load_reports_revenue', array($this, 'load_reports_revenue'));
        add_action('wp_ajax_bookinn_load_reports_occupancy', array($this, 'load_reports_occupancy'));
        add_action('wp_ajax_bookinn_load_reports_guests', array($this, 'load_reports_guests'));
        add_action('wp_ajax_bookinn_load_reports_performance', array($this, 'load_reports_performance'));
        
        // Export reports
        add_action('wp_ajax_bookinn_export_report', array($this, 'export_report'));
        
        // Booking counts
        add_action('wp_ajax_bookinn_get_booking_counts', array($this, 'get_booking_counts'));
        
        // New booking form
        add_action('wp_ajax_bookinn_get_new_booking_form', array($this, 'get_new_booking_form'));
        
        // Booking details
        add_action('wp_ajax_bookinn_get_booking_details', array($this, 'get_booking_details'));
        
        // Booking actions
        add_action('wp_ajax_bookinn_checkin_booking', array($this, 'checkin_booking'));
        add_action('wp_ajax_bookinn_checkout_booking', array($this, 'checkout_booking'));
        add_action('wp_ajax_bookinn_delete_booking', array($this, 'delete_booking'));
        
        // Create new booking
        add_action('wp_ajax_bookinn_create_booking', array($this, 'create_booking'));
    }
    
    /**
     * Load dashboard content
     */
    public function load_dashboard_content() {
        check_ajax_referer('bookinn_nonce', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_send_json_error(__('Insufficient permissions.', 'bookinn'));
        }
        
        ob_start();
        $this->render_dashboard_content();
        $html = ob_get_clean();
        
        wp_send_json_success(array('html' => $html));
    }
    
    /**
     * Load bookings content
     */
    public function load_bookings_content() {
        check_ajax_referer('bookinn_nonce', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_send_json_error(__('Insufficient permissions.', 'bookinn'));
        }
        
        ob_start();
        $this->render_bookings_content();
        $html = ob_get_clean();
        
        wp_send_json_success(array('html' => $html));
    }
    
    /**
     * Load rooms content
     */
    public function load_rooms_content() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        ob_start();
        $this->render_rooms_content();
        $html = ob_get_clean();
        
        wp_send_json_success(array('html' => $html));
    }
    
    /**
     * Load calendar content
     */
    public function load_calendar_content() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        ob_start();
        $this->render_calendar_content();
        $html = ob_get_clean();
        
        wp_send_json_success(array('html' => $html));
    }
    
    /**
     * Load reports overview content
     */
    public function load_reports_overview() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $date_range = sanitize_text_field($_POST['date_range'] ?? 'last_30_days');
        $date_from = sanitize_text_field($_POST['date_from'] ?? '');
        $date_to = sanitize_text_field($_POST['date_to'] ?? '');
        
        ob_start();
        $this->render_reports_overview($date_range, $date_from, $date_to);
        $html = ob_get_clean();
        
        $chart_data = $this->get_overview_chart_data($date_range, $date_from, $date_to);
        
        wp_send_json_success(array(
            'html' => $html,
            'chartData' => $chart_data
        ));
    }
    
    /**
     * Load reports revenue content
     */
    public function load_reports_revenue() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $date_range = sanitize_text_field($_POST['date_range'] ?? 'last_30_days');
        $date_from = sanitize_text_field($_POST['date_from'] ?? '');
        $date_to = sanitize_text_field($_POST['date_to'] ?? '');
        
        ob_start();
        $this->render_reports_revenue($date_range, $date_from, $date_to);
        $html = ob_get_clean();
        
        $chart_data = $this->get_revenue_chart_data($date_range, $date_from, $date_to);
        
        wp_send_json_success(array(
            'html' => $html,
            'chartData' => $chart_data
        ));
    }
    
    /**
     * Load reports occupancy content
     */
    public function load_reports_occupancy() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $date_range = sanitize_text_field($_POST['date_range'] ?? 'last_30_days');
        $date_from = sanitize_text_field($_POST['date_from'] ?? '');
        $date_to = sanitize_text_field($_POST['date_to'] ?? '');
        
        ob_start();
        $this->render_reports_occupancy($date_range, $date_from, $date_to);
        $html = ob_get_clean();
        
        $chart_data = $this->get_occupancy_chart_data($date_range, $date_from, $date_to);
        
        wp_send_json_success(array(
            'html' => $html,
            'chartData' => $chart_data
        ));
    }
    
    /**
     * Load reports guests content
     */
    public function load_reports_guests() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $date_range = sanitize_text_field($_POST['date_range'] ?? 'last_30_days');
        $date_from = sanitize_text_field($_POST['date_from'] ?? '');
        $date_to = sanitize_text_field($_POST['date_to'] ?? '');
        
        ob_start();
        $this->render_reports_guests($date_range, $date_from, $date_to);
        $html = ob_get_clean();
        
        $chart_data = $this->get_guests_chart_data($date_range, $date_from, $date_to);
        
        wp_send_json_success(array(
            'html' => $html,
            'chartData' => $chart_data
        ));
    }
    
    /**
     * Load reports performance content
     */
    public function load_reports_performance() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $date_range = sanitize_text_field($_POST['date_range'] ?? 'last_30_days');
        $date_from = sanitize_text_field($_POST['date_from'] ?? '');
        $date_to = sanitize_text_field($_POST['date_to'] ?? '');
        
        ob_start();
        $this->render_reports_performance($date_range, $date_from, $date_to);
        $html = ob_get_clean();
        
        $chart_data = $this->get_performance_chart_data($date_range, $date_from, $date_to);
        
        wp_send_json_success(array(
            'html' => $html,
            'chartData' => $chart_data
        ));
    }
    
    /**
     * Export report
     */
    public function export_report() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $tab = sanitize_text_field($_POST['tab'] ?? 'overview');
        $format = sanitize_text_field($_POST['format'] ?? 'pdf');
        $date_range = sanitize_text_field($_POST['date_range'] ?? 'last_30_days');
        $date_from = sanitize_text_field($_POST['date_from'] ?? '');
        $date_to = sanitize_text_field($_POST['date_to'] ?? '');
        
        // Generate export file
        $this->generate_report_export($tab, $format, $date_range, $date_from, $date_to);
    }
    
    /**
     * Get booking counts
     */
    public function get_booking_counts() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $counts = $this->get_booking_statistics();
        
        wp_send_json_success(array('counts' => $counts));
    }
    
    /**
     * Get new booking form
     */
    public function get_new_booking_form() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        ob_start();
        $this->render_new_booking_form();
        $html = ob_get_clean();
        
        wp_send_json_success(array('html' => $html));
    }
    
    /**
     * Render dashboard content
     */
    private function render_dashboard_content() {
        $stats = $this->get_booking_statistics();
        $recent_bookings = $this->get_recent_bookings(5);
        $upcoming_checkins = $this->get_upcoming_checkins(5);
        $current_occupancy = $this->get_current_occupancy();
        ?>
        
        <!-- Dashboard Overview Cards -->
        <div class="bookinn-dashboard-cards">
            <div class="bookinn-stats-grid">
                
                <div class="bookinn-stat-card">
                    <div class="bookinn-stat-icon">
                        <i class="bookinn-icon-bookings"></i>
                    </div>
                    <div class="bookinn-stat-content">
                        <h3><?php echo esc_html($stats['total_bookings']); ?></h3>
                        <p><?php _e('Total Bookings', 'bookinn'); ?></p>
                    </div>
                </div>
                
                <div class="bookinn-stat-card">
                    <div class="bookinn-stat-icon">
                        <i class="bookinn-icon-checkin"></i>
                    </div>
                    <div class="bookinn-stat-content">
                        <h3><?php echo esc_html($stats['todays_checkins']); ?></h3>
                        <p><?php _e('Today\'s Check-ins', 'bookinn'); ?></p>
                    </div>
                </div>
                
                <div class="bookinn-stat-card">
                    <div class="bookinn-stat-icon">
                        <i class="bookinn-icon-checkout"></i>
                    </div>
                    <div class="bookinn-stat-content">
                        <h3><?php echo esc_html($stats['todays_checkouts']); ?></h3>
                        <p><?php _e('Today\'s Check-outs', 'bookinn'); ?></p>
                    </div>
                </div>
                
                <div class="bookinn-stat-card">
                    <div class="bookinn-stat-icon">
                        <i class="bookinn-icon-occupancy"></i>
                    </div>
                    <div class="bookinn-stat-content">
                        <h3><?php echo esc_html($current_occupancy); ?>%</h3>
                        <p><?php _e('Current Occupancy', 'bookinn'); ?></p>
                    </div>
                </div>
                
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="bookinn-dashboard-sections">
            
            <!-- Recent Bookings -->
            <div class="bookinn-dashboard-section">
                <h3><?php _e('Recent Bookings', 'bookinn'); ?></h3>
                <?php if (!empty($recent_bookings)): ?>
                    <div class="bookinn-recent-bookings">
                        <?php foreach ($recent_bookings as $booking): ?>
                            <div class="bookinn-booking-item">
                                <div class="bookinn-booking-guest">
                                    <strong><?php echo esc_html($booking['guest_name']); ?></strong>
                                    <span class="bookinn-booking-dates">
                                        <?php echo esc_html($booking['check_in']); ?> - <?php echo esc_html($booking['check_out']); ?>
                                    </span>
                                </div>
                                <div class="bookinn-booking-status">
                                    <span class="bookinn-status bookinn-status-<?php echo esc_attr($booking['status']); ?>">
                                        <?php echo esc_html($booking['status_label']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="bookinn-no-data"><?php _e('No recent bookings found.', 'bookinn'); ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Upcoming Check-ins -->
            <div class="bookinn-dashboard-section">
                <h3><?php _e('Upcoming Check-ins', 'bookinn'); ?></h3>
                <?php if (!empty($upcoming_checkins)): ?>
                    <div class="bookinn-upcoming-checkins">
                        <?php foreach ($upcoming_checkins as $checkin): ?>
                            <div class="bookinn-checkin-item">
                                <div class="bookinn-checkin-info">
                                    <strong><?php echo esc_html($checkin['guest_name']); ?></strong>
                                    <span class="bookinn-checkin-date">
                                        <?php echo esc_html($checkin['check_in']); ?>
                                    </span>
                                    <span class="bookinn-room-info">
                                        <?php _e('Room:', 'bookinn'); ?> <?php echo esc_html($checkin['room_name']); ?>
                                    </span>
                                </div>
                                <div class="bookinn-checkin-actions">
                                    <button type="button" class="bookinn-btn bookinn-btn-sm bookinn-btn-primary" 
                                            data-booking-action="checkin" 
                                            data-booking-id="<?php echo esc_attr($checkin['id']); ?>">
                                        <?php _e('Check In', 'bookinn'); ?>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="bookinn-no-data"><?php _e('No upcoming check-ins today.', 'bookinn'); ?></p>
                <?php endif; ?>
            </div>
            
        </div>
        
        <?php
    }
    
    /**
     * Render bookings content
     */
    private function render_bookings_content() {
        $bookings = $this->get_all_bookings();
        ?>
        
        <!-- Bookings Filters -->
        <div class="bookinn-bookings-filters">
            <div class="bookinn-filters-row">
                <select name="status_filter" class="bookinn-filter-select">
                    <option value=""><?php _e('All Statuses', 'bookinn'); ?></option>
                    <option value="confirmed"><?php _e('Confirmed', 'bookinn'); ?></option>
                    <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                    <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                </select>
                
                <input type="date" name="date_from" class="bookinn-filter-date" placeholder="<?php _e('From Date', 'bookinn'); ?>">
                <input type="date" name="date_to" class="bookinn-filter-date" placeholder="<?php _e('To Date', 'bookinn'); ?>">
                
                <button type="button" class="bookinn-btn bookinn-btn-secondary" data-action="filter-bookings">
                    <?php _e('Filter', 'bookinn'); ?>
                </button>
            </div>
        </div>
        
        <!-- Bookings Table -->
        <div class="bookinn-bookings-table-wrapper">
            <?php if (!empty($bookings)): ?>
                <table class="bookinn-bookings-table">
                    <thead>
                        <tr>
                            <th><?php _e('Guest', 'bookinn'); ?></th>
                            <th><?php _e('Dates', 'bookinn'); ?></th>
                            <th><?php _e('Room', 'bookinn'); ?></th>
                            <th><?php _e('Status', 'bookinn'); ?></th>
                            <th><?php _e('Total', 'bookinn'); ?></th>
                            <th><?php _e('Actions', 'bookinn'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($bookings as $booking): ?>
                            <tr>
                                <td>
                                    <div class="bookinn-guest-info">
                                        <strong><?php echo esc_html($booking['guest_name']); ?></strong>
                                        <span><?php echo esc_html($booking['guest_email']); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="bookinn-booking-dates">
                                        <span class="bookinn-checkin"><?php echo esc_html($booking['check_in']); ?></span>
                                        <span class="bookinn-checkout"><?php echo esc_html($booking['check_out']); ?></span>
                                    </div>
                                </td>
                                <td><?php echo esc_html($booking['room_name']); ?></td>
                                <td>
                                    <span class="bookinn-status bookinn-status-<?php echo esc_attr($booking['status']); ?>">
                                        <?php echo esc_html($booking['status_label']); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html($booking['total_formatted']); ?></td>
                                <td>
                                    <div class="bookinn-actions">
                                        <button type="button" class="bookinn-btn bookinn-btn-sm" 
                                                data-booking-action="view" 
                                                data-booking-id="<?php echo esc_attr($booking['id']); ?>">
                                            <?php _e('View', 'bookinn'); ?>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="bookinn-no-data">
                    <p><?php _e('No bookings found.', 'bookinn'); ?></p>
                    <button type="button" class="bookinn-btn bookinn-btn-primary" data-action="new-booking">
                        <?php _e('Create First Booking', 'bookinn'); ?>
                    </button>
                </div>
            <?php endif; ?>
        </div>
        
        <?php
    }
    
    /**
     * Render rooms content
     */
    private function render_rooms_content() {
        $rooms = $this->get_all_rooms();
        ?>
        
        <!-- Rooms Grid -->
        <div class="bookinn-rooms-grid">
            <?php if (!empty($rooms)): ?>
                <?php foreach ($rooms as $room): ?>
                    <div class="bookinn-room-card">
                        <div class="bookinn-room-header">
                            <h4><?php echo esc_html($room['name']); ?></h4>
                            <span class="bookinn-room-type"><?php echo esc_html($room['type']); ?></span>
                        </div>
                        
                        <div class="bookinn-room-details">
                            <div class="bookinn-room-capacity">
                                <i class="bookinn-icon-guests"></i>
                                <?php printf(__('%d guests', 'bookinn'), $room['max_guests']); ?>
                            </div>
                            
                            <div class="bookinn-room-price">
                                <strong><?php echo esc_html($room['price_formatted']); ?></strong>
                                <span>/<?php _e('night', 'bookinn'); ?></span>
                            </div>
                        </div>
                        
                        <div class="bookinn-room-status">
                            <span class="bookinn-status bookinn-status-<?php echo esc_attr($room['status']); ?>">
                                <?php echo esc_html($room['status_label']); ?>
                            </span>
                        </div>
                        
                        <div class="bookinn-room-actions">
                            <button type="button" class="bookinn-btn bookinn-btn-sm" 
                                    data-room-action="view" 
                                    data-room-id="<?php echo esc_attr($room['id']); ?>">
                                <?php _e('View', 'bookinn'); ?>
                            </button>
                            <button type="button" class="bookinn-btn bookinn-btn-sm" 
                                    data-room-action="edit" 
                                    data-room-id="<?php echo esc_attr($room['id']); ?>">
                                <?php _e('Edit', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="bookinn-no-data">
                    <p><?php _e('No rooms configured yet.', 'bookinn'); ?></p>
                    <button type="button" class="bookinn-btn bookinn-btn-primary" data-action="new-room">
                        <?php _e('Add First Room', 'bookinn'); ?>
                    </button>
                </div>
            <?php endif; ?>
        </div>
        
        <?php
    }
    
    /**
     * Render calendar content
     */
    private function render_calendar_content() {
        ?>
        
        <!-- Calendar View -->
        <div class="bookinn-calendar-container">
            <div class="bookinn-calendar-header">
                <div class="bookinn-calendar-navigation">
                    <button type="button" class="bookinn-btn bookinn-btn-secondary" data-action="prev-month">
                        &lt; <?php _e('Previous', 'bookinn'); ?>
                    </button>
                    <h3 class="bookinn-calendar-title"><?php echo date('F Y'); ?></h3>
                    <button type="button" class="bookinn-btn bookinn-btn-secondary" data-action="next-month">
                        <?php _e('Next', 'bookinn'); ?> &gt;
                    </button>
                </div>
                
                <div class="bookinn-calendar-legend">
                    <span class="bookinn-legend-item">
                        <span class="bookinn-legend-color bookinn-available"></span>
                        <?php _e('Available', 'bookinn'); ?>
                    </span>
                    <span class="bookinn-legend-item">
                        <span class="bookinn-legend-color bookinn-booked"></span>
                        <?php _e('Booked', 'bookinn'); ?>
                    </span>
                    <span class="bookinn-legend-item">
                        <span class="bookinn-legend-color bookinn-checkin"></span>
                        <?php _e('Check-in', 'bookinn'); ?>
                    </span>
                    <span class="bookinn-legend-item">
                        <span class="bookinn-legend-color bookinn-checkout"></span>
                        <?php _e('Check-out', 'bookinn'); ?>
                    </span>
                </div>
            </div>
            
            <div id="bookinn-calendar" class="bookinn-calendar-view">
                <!-- Calendar will be rendered here via JavaScript -->
                <p><?php _e('Loading calendar...', 'bookinn'); ?></p>
            </div>
        </div>
        
        <?php
    }
    
    /**
     * Render reports content
     */
    private function render_reports_content() {
        $reports_data = $this->get_reports_data();
        ?>
        
        <!-- Reports Filters -->
        <div class="bookinn-reports-filters">
            <div class="bookinn-filters-row">
                <select name="report_period" class="bookinn-report-filter">
                    <option value="last_30_days"><?php _e('Last 30 Days', 'bookinn'); ?></option>
                    <option value="this_month"><?php _e('This Month', 'bookinn'); ?></option>
                    <option value="last_month"><?php _e('Last Month', 'bookinn'); ?></option>
                    <option value="this_year"><?php _e('This Year', 'bookinn'); ?></option>
                    <option value="custom"><?php _e('Custom Range', 'bookinn'); ?></option>
                </select>
                
                <input type="date" name="report_from" class="bookinn-report-filter" style="display: none;">
                <input type="date" name="report_to" class="bookinn-report-filter" style="display: none;">
                
                <button type="button" class="bookinn-btn bookinn-btn-secondary" data-action="generate-report">
                    <?php _e('Generate Report', 'bookinn'); ?>
                </button>
            </div>
        </div>
        
        <!-- Revenue Chart -->
        <div class="bookinn-reports-section">
            <h3><?php _e('Revenue Overview', 'bookinn'); ?></h3>
            <div class="bookinn-chart-container">
                <canvas id="bookinn-revenue-chart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <!-- Occupancy Chart -->
        <div class="bookinn-reports-section">
            <h3><?php _e('Occupancy Rate', 'bookinn'); ?></h3>
            <div class="bookinn-chart-container">
                <canvas id="bookinn-occupancy-chart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <!-- Summary Stats -->
        <div class="bookinn-reports-summary">
            <div class="bookinn-summary-grid">
                <div class="bookinn-summary-item">
                    <h4><?php _e('Total Revenue', 'bookinn'); ?></h4>
                    <span class="bookinn-summary-value"><?php echo esc_html($reports_data['total_revenue']); ?></span>
                </div>
                
                <div class="bookinn-summary-item">
                    <h4><?php _e('Total Bookings', 'bookinn'); ?></h4>
                    <span class="bookinn-summary-value"><?php echo esc_html($reports_data['total_bookings']); ?></span>
                </div>
                
                <div class="bookinn-summary-item">
                    <h4><?php _e('Average Stay', 'bookinn'); ?></h4>
                    <span class="bookinn-summary-value"><?php echo esc_html($reports_data['average_stay']); ?> <?php _e('nights', 'bookinn'); ?></span>
                </div>
                
                <div class="bookinn-summary-item">
                    <h4><?php _e('Occupancy Rate', 'bookinn'); ?></h4>
                    <span class="bookinn-summary-value"><?php echo esc_html($reports_data['occupancy_rate']); ?>%</span>
                </div>
            </div>
        </div>
        
        <?php
    }
    
    /**
     * Render new booking form
     */
    private function render_new_booking_form() {
        $rooms = $this->get_available_rooms();
        ?>
        
        <form id="bookinn-new-booking-form" class="bookinn-booking-form">
            <?php wp_nonce_field('bookinn_new_booking', 'booking_nonce'); ?>
            
            <div class="bookinn-form-row">
                <div class="bookinn-form-field">
                    <label for="guest_name"><?php _e('Guest Name', 'bookinn'); ?> *</label>
                    <input type="text" id="guest_name" name="guest_name" required>
                </div>
                
                <div class="bookinn-form-field">
                    <label for="guest_email"><?php _e('Email', 'bookinn'); ?> *</label>
                    <input type="email" id="guest_email" name="guest_email" required>
                </div>
            </div>
            
            <div class="bookinn-form-row">
                <div class="bookinn-form-field">
                    <label for="guest_phone"><?php _e('Phone', 'bookinn'); ?></label>
                    <input type="tel" id="guest_phone" name="guest_phone">
                </div>
                
                <div class="bookinn-form-field">
                    <label for="room_id"><?php _e('Room', 'bookinn'); ?> *</label>
                    <select id="room_id" name="room_id" required>
                        <option value=""><?php _e('Select Room', 'bookinn'); ?></option>
                        <?php foreach ($rooms as $room): ?>
                            <option value="<?php echo esc_attr($room['id']); ?>">
                                <?php echo esc_html($room['name']); ?> - <?php echo esc_html($room['price_formatted']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="bookinn-form-row">
                <div class="bookinn-form-field">
                    <label for="check_in"><?php _e('Check-in Date', 'bookinn'); ?> *</label>
                    <input type="date" id="check_in" name="check_in" required>
                </div>
                
                <div class="bookinn-form-field">
                    <label for="check_out"><?php _e('Check-out Date', 'bookinn'); ?> *</label>
                    <input type="date" id="check_out" name="check_out" required>
                </div>
            </div>
            
            <div class="bookinn-form-row">
                <div class="bookinn-form-field">
                    <label for="adults"><?php _e('Adults', 'bookinn'); ?></label>
                    <select id="adults" name="adults">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                    </select>
                </div>
                
                <div class="bookinn-form-field">
                    <label for="children"><?php _e('Children', 'bookinn'); ?></label>
                    <select id="children" name="children">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                    </select>
                </div>
            </div>
            
            <div class="bookinn-form-field">
                <label for="notes"><?php _e('Notes', 'bookinn'); ?></label>
                <textarea id="notes" name="notes" rows="3"></textarea>
            </div>
            
            <div class="bookinn-form-actions">
                <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-modal-close">
                    <?php _e('Cancel', 'bookinn'); ?>
                </button>
                <button type="submit" class="bookinn-btn bookinn-btn-primary">
                    <?php _e('Create Booking', 'bookinn'); ?>
                </button>
            </div>
        </form>
        
        <?php
    }
    
    /**
     * Get booking statistics (using real database queries)
     */
    private function get_booking_statistics() {
        return BookInn_Models::get_booking_statistics();
    }
    
    /**
     * Get current occupancy (using real database queries)
     */
    private function get_current_occupancy() {
        return BookInn_Models::get_current_occupancy();
    }
    
    /**
     * Get recent bookings (using real database queries)
     */
    private function get_recent_bookings($limit = 5) {
        return BookInn_Models::get_recent_bookings($limit);
    }
    
    /**
     * Get upcoming checkins (using real database queries)
     */
    private function get_upcoming_checkins($limit = 5) {
        return BookInn_Models::get_upcoming_checkins($limit);
    }
    
    /**
     * Get all bookings (using real database queries)
     */
    private function get_all_bookings() {
        $filters = array();
        
        if (isset($_POST['status_filter']) && !empty($_POST['status_filter'])) {
            $filters['status'] = sanitize_text_field($_POST['status_filter']);
        }
        
        if (isset($_POST['date_from']) && !empty($_POST['date_from'])) {
            $filters['date_from'] = sanitize_text_field($_POST['date_from']);
        }
        
        if (isset($_POST['date_to']) && !empty($_POST['date_to'])) {
            $filters['date_to'] = sanitize_text_field($_POST['date_to']);
        }
        
        return BookInn_Models::get_all_bookings($filters);
    }
    
    /**
     * Get all rooms (using real database queries)
     */
    private function get_all_rooms() {
        return BookInn_Models::get_all_rooms();
    }
    
    /**
     * Get available rooms (using real database queries)
     */
    private function get_available_rooms() {
        return BookInn_Models::get_available_rooms();
    }
    
    /**
     * Get reports data (using real database queries)
     */
    private function get_reports_data() {
        $period = isset($_POST['report_period']) ? sanitize_text_field($_POST['report_period']) : 'last_30_days';
        $date_from = isset($_POST['report_from']) ? sanitize_text_field($_POST['report_from']) : null;
        $date_to = isset($_POST['report_to']) ? sanitize_text_field($_POST['report_to']) : null;
        
        return BookInn_Models::get_reports_data($period, $date_from, $date_to);
    }
    
    /**
     * Render reports overview
     */
    private function render_reports_overview($date_range, $date_from = '', $date_to = '') {
        $stats = BookInn_Models::get_reports_data($date_range, $date_from, $date_to);
        $recent_bookings = BookInn_Models::get_recent_bookings(10);
        $occupancy_rate = BookInn_Models::get_current_occupancy();
        ?>
        
        <!-- Key Metrics Grid -->
        <div class="bookinn-stats-grid">
            <div class="bookinn-stat-card">
                <div class="bookinn-stat-icon">
                    <i class="bookinn-icon-revenue"></i>
                </div>
                <div class="bookinn-stat-content">
                    <h3><?php echo esc_html($stats['total_revenue']); ?></h3>
                    <p><?php _e('Total Revenue', 'bookinn'); ?></p>
                </div>
            </div>
            
            <div class="bookinn-stat-card">
                <div class="bookinn-stat-icon">
                    <i class="bookinn-icon-bookings"></i>
                </div>
                <div class="bookinn-stat-content">
                    <h3><?php echo esc_html($stats['total_bookings']); ?></h3>
                    <p><?php _e('Total Bookings', 'bookinn'); ?></p>
                </div>
            </div>
            
            <div class="bookinn-stat-card">
                <div class="bookinn-stat-icon">
                    <i class="bookinn-icon-occupancy"></i>
                </div>
                <div class="bookinn-stat-content">
                    <h3><?php echo esc_html($occupancy_rate); ?>%</h3>
                    <p><?php _e('Occupancy Rate', 'bookinn'); ?></p>
                </div>
            </div>
            
            <div class="bookinn-stat-card">
                <div class="bookinn-stat-icon">
                    <i class="bookinn-icon-nights"></i>
                </div>
                <div class="bookinn-stat-content">
                    <h3><?php echo esc_html($stats['average_stay']); ?></h3>
                    <p><?php _e('Average Stay (nights)', 'bookinn'); ?></p>
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="bookinn-overview-charts">
            <div class="bookinn-chart-container">
                <h3><?php _e('Revenue Trend', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="overview-revenue-chart"></canvas>
                </div>
            </div>
            
            <div class="bookinn-chart-container">
                <h3><?php _e('Bookings Trend', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="overview-bookings-chart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="bookinn-recent-activity">
            <h3><?php _e('Recent Bookings', 'bookinn'); ?></h3>
            <?php if (!empty($recent_bookings)): ?>
                <table class="bookinn-bookings-table">
                    <thead>
                        <tr>
                            <th><?php _e('Guest', 'bookinn'); ?></th>
                            <th><?php _e('Dates', 'bookinn'); ?></th>
                            <th><?php _e('Status', 'bookinn'); ?></th>
                            <th><?php _e('Total', 'bookinn'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($recent_bookings, 0, 5) as $booking): ?>
                            <tr>
                                <td><?php echo esc_html($booking['guest_name']); ?></td>
                                <td><?php echo esc_html($booking['check_in']); ?> - <?php echo esc_html($booking['check_out']); ?></td>
                                <td><span class="bookinn-status bookinn-status-<?php echo esc_attr($booking['status']); ?>"><?php echo esc_html($booking['status_label']); ?></span></td>
                                <td><?php echo esc_html($booking['total_formatted'] ?? '€0.00'); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p class="bookinn-no-data"><?php _e('No recent bookings found.', 'bookinn'); ?></p>
            <?php endif; ?>
        </div>
        
        <?php
    }
    
    /**
     * Render reports revenue
     */
    private function render_reports_revenue($date_range, $date_from = '', $date_to = '') {
        $revenue_data = BookInn_Models::get_reports_data($date_range, $date_from, $date_to);
        ?>
        
        <!-- Revenue Summary -->
        <div class="bookinn-revenue-summary">
            <div class="bookinn-revenue-item">
                <h4><?php _e('Total Revenue', 'bookinn'); ?></h4>
                <p class="value"><?php echo esc_html($revenue_data['total_revenue']); ?></p>
            </div>
            
            <div class="bookinn-revenue-item">
                <h4><?php _e('Average Daily Rate', 'bookinn'); ?></h4>
                <p class="value">€95.50</p>
                <p class="change positive">+5.2%</p>
            </div>
            
            <div class="bookinn-revenue-item">
                <h4><?php _e('Revenue Per Room', 'bookinn'); ?></h4>
                <p class="value">€71.50</p>
                <p class="change positive">+3.1%</p>
            </div>
            
            <div class="bookinn-revenue-item">
                <h4><?php _e('Revenue Growth', 'bookinn'); ?></h4>
                <p class="value">+12.3%</p>
                <p class="change positive">vs. previous period</p>
            </div>
        </div>
        
        <!-- Revenue Charts -->
        <div class="bookinn-revenue-charts">
            <div class="bookinn-chart-container">
                <h3><?php _e('Monthly Revenue', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="revenue-monthly-chart"></canvas>
                </div>
            </div>
            
            <div class="bookinn-chart-container">
                <h3><?php _e('Revenue by Source', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="revenue-source-chart"></canvas>
                </div>
            </div>
        </div>
        
        <?php
    }
    
    /**
     * Render reports occupancy
     */
    private function render_reports_occupancy($date_range, $date_from = '', $date_to = '') {
        $occupancy_rate = BookInn_Models::get_current_occupancy();
        $rooms = BookInn_Models::get_all_rooms();
        ?>
        
        <!-- Occupancy Stats -->
        <div class="bookinn-occupancy-stats-grid">
            <div class="bookinn-stat-card">
                <h4><?php _e('Current Occupancy', 'bookinn'); ?></h4>
                <p class="value"><?php echo esc_html($occupancy_rate); ?>%</p>
            </div>
            
            <div class="bookinn-stat-card">
                <h4><?php _e('Available Rooms', 'bookinn'); ?></h4>
                <p class="value"><?php echo count(array_filter($rooms, function($room) { return $room['status'] === 'available'; })); ?></p>
            </div>
            
            <div class="bookinn-stat-card">
                <h4><?php _e('Occupied Rooms', 'bookinn'); ?></h4>
                <p class="value"><?php echo count(array_filter($rooms, function($room) { return $room['status'] === 'occupied'; })); ?></p>
            </div>
            
            <div class="bookinn-stat-card">
                <h4><?php _e('Out of Order', 'bookinn'); ?></h4>
                <p class="value"><?php echo count(array_filter($rooms, function($room) { return $room['status'] === 'maintenance'; })); ?></p>
            </div>
        </div>
        
        <!-- Occupancy Charts -->
        <div class="bookinn-occupancy-content">
            <div class="bookinn-chart-container">
                <h3><?php _e('Occupancy Rate Trend', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="occupancy-rate-chart"></canvas>
                </div>
            </div>
            
            <div class="bookinn-chart-container">
                <h3><?php _e('Room Type Occupancy', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="room-type-occupancy-chart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Room Status Grid -->
        <div class="bookinn-room-status-grid">
            <h3><?php _e('Room Status Overview', 'bookinn'); ?></h3>
            <div class="bookinn-occupancy-legend">
                <span class="bookinn-legend-item">
                    <span class="bookinn-legend-color available"></span>
                    <?php _e('Available', 'bookinn'); ?>
                </span>
                <span class="bookinn-legend-item">
                    <span class="bookinn-legend-color occupied"></span>
                    <?php _e('Occupied', 'bookinn'); ?>
                </span>
                <span class="bookinn-legend-item">
                    <span class="bookinn-legend-color maintenance"></span>
                    <?php _e('Maintenance', 'bookinn'); ?>
                </span>
                <span class="bookinn-legend-item">
                    <span class="bookinn-legend-color blocked"></span>
                    <?php _e('Out of Order', 'bookinn'); ?>
                </span>
            </div>
            
            <div class="bookinn-rooms-grid">
                <?php foreach ($rooms as $room): ?>
                    <div class="bookinn-room-status-item <?php echo esc_attr($room['status']); ?>">
                        <span class="room-number"><?php echo esc_html($room['name']); ?></span>
                        <span class="room-status"><?php echo esc_html($room['status_label']); ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <?php
    }
    
    /**
     * Render reports guests
     */
    private function render_reports_guests($date_range, $date_from = '', $date_to = '') {
        ?>
        
        <!-- Guest Metrics -->
        <div class="bookinn-guest-metrics">
            <div class="bookinn-stat-card">
                <h4><?php _e('Total Guests', 'bookinn'); ?></h4>
                <p class="value">1,245</p>
                <p class="change positive">+8.3%</p>
            </div>
            
            <div class="bookinn-stat-card">
                <h4><?php _e('New Guests', 'bookinn'); ?></h4>
                <p class="value">876</p>
                <p class="change positive">+12.1%</p>
            </div>
            
            <div class="bookinn-stat-card">
                <h4><?php _e('Returning Guests', 'bookinn'); ?></h4>
                <p class="value">369</p>
                <p class="change neutral">+2.5%</p>
            </div>
            
            <div class="bookinn-stat-card">
                <h4><?php _e('Guest Satisfaction', 'bookinn'); ?></h4>
                <p class="value">4.7/5</p>
                <p class="change positive">+0.2</p>
            </div>
        </div>
        
        <!-- Guest Analytics -->
        <div class="bookinn-guests-content">
            <div class="bookinn-chart-container">
                <h3><?php _e('Guest Segments', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="guest-segments-chart"></canvas>
                </div>
            </div>
            
            <div class="bookinn-chart-container">
                <h3><?php _e('Guest Trends', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="guest-trends-chart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Top Guests -->
        <div class="bookinn-top-guests">
            <h3><?php _e('Top Guests by Bookings', 'bookinn'); ?></h3>
            <div class="bookinn-guest-list">
                <table>
                    <thead>
                        <tr>
                            <th><?php _e('Guest Name', 'bookinn'); ?></th>
                            <th><?php _e('Total Bookings', 'bookinn'); ?></th>
                            <th><?php _e('Total Spent', 'bookinn'); ?></th>
                            <th><?php _e('Last Visit', 'bookinn'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>John Smith</td>
                            <td>8</td>
                            <td>€2,450.00</td>
                            <td>15/01/2025</td>
                        </tr>
                        <tr>
                            <td>Mary Johnson</td>
                            <td>6</td>
                            <td>€1,890.00</td>
                            <td>12/01/2025</td>
                        </tr>
                        <tr>
                            <td>Robert Brown</td>
                            <td>5</td>
                            <td>€1,520.00</td>
                            <td>08/01/2025</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <?php
    }
    
    /**
     * Render reports performance
     */
    private function render_reports_performance($date_range, $date_from = '', $date_to = '') {
        ?>
        
        <!-- Performance KPIs -->
        <div class="bookinn-performance-content">
            <div class="bookinn-performance-metric">
                <h4><?php _e('Average Daily Rate (ADR)', 'bookinn'); ?></h4>
                <div class="bookinn-metric-value">€95.50</div>
                <div class="bookinn-metric-description"><?php _e('Average rate per occupied room', 'bookinn'); ?></div>
                <div class="bookinn-metric-trend up">
                    <span>↗</span> +5.2% vs. last period
                </div>
            </div>
            
            <div class="bookinn-performance-metric">
                <h4><?php _e('Revenue Per Available Room (RevPAR)', 'bookinn'); ?></h4>
                <div class="bookinn-metric-value">€71.50</div>
                <div class="bookinn-metric-description"><?php _e('Revenue per available room', 'bookinn'); ?></div>
                <div class="bookinn-metric-trend up">
                    <span>↗</span> +3.1% vs. last period
                </div>
            </div>
            
            <div class="bookinn-performance-metric">
                <h4><?php _e('Gross Operating Profit Per Available Room (GOPPAR)', 'bookinn'); ?></h4>
                <div class="bookinn-metric-value">€45.20</div>
                <div class="bookinn-metric-description"><?php _e('Operating profit per available room', 'bookinn'); ?></div>
                <div class="bookinn-metric-trend up">
                    <span>↗</span> +4.8% vs. last period
                </div>
            </div>
            
            <div class="bookinn-performance-metric">
                <h4><?php _e('Average Length of Stay (ALOS)', 'bookinn'); ?></h4>
                <div class="bookinn-metric-value">2.3</div>
                <div class="bookinn-metric-description"><?php _e('Average nights per booking', 'bookinn'); ?></div>
                <div class="bookinn-metric-trend down">
                    <span>↘</span> -0.2 vs. last period
                </div>
            </div>
            
            <div class="bookinn-performance-metric">
                <h4><?php _e('Cancellation Rate', 'bookinn'); ?></h4>
                <div class="bookinn-metric-value">8.5%</div>
                <div class="bookinn-metric-description"><?php _e('Percentage of bookings cancelled', 'bookinn'); ?></div>
                <div class="bookinn-metric-trend down">
                    <span>↘</span> -1.2% vs. last period
                </div>
            </div>
            
            <div class="bookinn-performance-metric">
                <h4><?php _e('No-Show Rate', 'bookinn'); ?></h4>
                <div class="bookinn-metric-value">2.1%</div>
                <div class="bookinn-metric-description"><?php _e('Percentage of guests who didn\'t show up', 'bookinn'); ?></div>
                <div class="bookinn-metric-trend down">
                    <span>↘</span> -0.5% vs. last period
                </div>
            </div>
        </div>
        
        <!-- Performance Charts -->
        <div class="bookinn-performance-charts">
            <div class="bookinn-chart-container">
                <h3><?php _e('ADR Trend', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="adr-trend-chart"></canvas>
                </div>
            </div>
            
            <div class="bookinn-chart-container">
                <h3><?php _e('RevPAR Trend', 'bookinn'); ?></h3>
                <div class="bookinn-chart-wrapper">
                    <canvas id="revpar-trend-chart"></canvas>
                </div>
            </div>
        </div>
        
        <?php
    }
    
    /**
     * Get chart data methods
     */
    private function get_overview_chart_data($date_range, $date_from = '', $date_to = '') {
        // Mock data - replace with real database queries
        return array(
            'revenueTrend' => array(
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data' => [1200, 1500, 1800, 2100, 1900, 2300]
            ),
            'bookingsTrend' => array(
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data' => [15, 20, 25, 30, 28, 35]
            )
        );
    }
    
    private function get_revenue_chart_data($date_range, $date_from = '', $date_to = '') {
        return array(
            'monthlyRevenue' => array(
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data' => [1200, 1500, 1800, 2100, 1900, 2300]
            ),
            'revenueBySource' => array(
                'labels' => ['Direct', 'Booking.com', 'Airbnb', 'Phone', 'Walk-in'],
                'data' => [40, 25, 20, 10, 5]
            )
        );
    }
    
    private function get_occupancy_chart_data($date_range, $date_from = '', $date_to = '') {
        return array(
            'occupancyRate' => array(
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data' => [65, 72, 78, 85, 82, 88]
            ),
            'roomTypeOccupancy' => array(
                'labels' => ['Standard', 'Deluxe', 'Suite', 'Family'],
                'data' => [85, 78, 65, 72]
            )
        );
    }
    
    private function get_guests_chart_data($date_range, $date_from = '', $date_to = '') {
        return array(
            'guestSegments' => array(
                'labels' => ['Business', 'Leisure', 'Group', 'Corporate', 'Other'],
                'data' => [35, 45, 10, 8, 2]
            ),
            'guestTrends' => array(
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'new' => [12, 15, 20, 25, 22, 28],
                'returning' => [8, 10, 12, 15, 18, 20]
            )
        );
    }
    
    private function get_performance_chart_data($date_range, $date_from = '', $date_to = '') {
        return array(
            'adrTrend' => array(
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data' => [85, 90, 95, 98, 92, 96]
            ),
            'revparTrend' => array(
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data' => [55, 65, 74, 83, 76, 85]
            )
        );
    }
    
    /**
     * Generate report export
     */
    private function generate_report_export($tab, $format, $date_range, $date_from = '', $date_to = '') {
        // Basic CSV export for now
        if ($format === 'csv') {
            $filename = "bookinn-{$tab}-report-" . date('Y-m-d') . '.csv';
            
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            $output = fopen('php://output', 'w');
            
            // Add CSV headers
            fputcsv($output, array('Report', 'Period', 'Generated'));
            fputcsv($output, array(ucfirst($tab) . ' Report', $date_range, date('Y-m-d H:i:s')));
            fputcsv($output, array()); // Empty row
            
            // Add data based on tab
            switch ($tab) {
                case 'overview':
                    $stats = BookInn_Models::get_reports_data($date_range, $date_from, $date_to);
                    fputcsv($output, array('Metric', 'Value'));
                    fputcsv($output, array('Total Revenue', $stats['total_revenue']));
                    fputcsv($output, array('Total Bookings', $stats['total_bookings']));
                    fputcsv($output, array('Average Stay', $stats['average_stay']));
                    fputcsv($output, array('Occupancy Rate', $stats['occupancy_rate'] . '%'));
                    break;
                    
                case 'revenue':
                    fputcsv($output, array('Metric', 'Value'));
                    fputcsv($output, array('Total Revenue', '€2,450.00'));
                    fputcsv($output, array('Average Daily Rate', '€95.50'));
                    fputcsv($output, array('Revenue Per Room', '€71.50'));
                    break;
                    
                // Add more cases for other tabs
            }
            
            fclose($output);
            exit;
        }
        
        // For PDF or other formats, you would implement additional libraries
        wp_die(__('Export format not supported yet.', 'bookinn'));
    }
    
    /**
     * Check-in booking
     */
    public function checkin_booking() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $booking_id = intval($_POST['booking_id']);
        
        if (!$booking_id) {
            wp_send_json_error(array('message' => __('Invalid booking ID.', 'bookinn')));
        }
        
        global $wpdb;
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        
        $result = $wpdb->update(
            $table_bookings,
            array(
                'status' => 'checked_in',
                'check_in_time' => current_time('mysql')
            ),
            array('id' => $booking_id),
            array('%s', '%s'),
            array('%d')
        );
        
        if ($result !== false) {
            wp_send_json_success(array('message' => __('Guest checked in successfully.', 'bookinn')));
        } else {
            wp_send_json_error(array('message' => __('Failed to check in guest.', 'bookinn')));
        }
    }
    
    /**
     * Check-out booking
     */
    public function checkout_booking() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $booking_id = intval($_POST['booking_id']);
        
        if (!$booking_id) {
            wp_send_json_error(array('message' => __('Invalid booking ID.', 'bookinn')));
        }
        
        global $wpdb;
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        
        $result = $wpdb->update(
            $table_bookings,
            array(
                'status' => 'checked_out',
                'check_out_time' => current_time('mysql')
            ),
            array('id' => $booking_id),
            array('%s', '%s'),
            array('%d')
        );
        
        if ($result !== false) {
            wp_send_json_success(array('message' => __('Guest checked out successfully.', 'bookinn')));
        } else {
            wp_send_json_error(array('message' => __('Failed to check out guest.', 'bookinn')));
        }
    }
    
    /**
     * Delete booking
     */
    public function delete_booking() {
        check_ajax_referer('bookinn_ajax', 'nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        $booking_id = intval($_POST['booking_id']);
        
        if (!$booking_id) {
            wp_send_json_error(array('message' => __('Invalid booking ID.', 'bookinn')));
        }
        
        global $wpdb;
        $table_bookings = $wpdb->prefix . 'bookinn_bookings';
        
        $result = $wpdb->update(
            $table_bookings,
            array('status' => 'cancelled'),
            array('id' => $booking_id),
            array('%s'),
            array('%d')
        );
        
        if ($result !== false) {
            wp_send_json_success(array('message' => __('Booking cancelled successfully.', 'bookinn')));
        } else {
            wp_send_json_error(array('message' => __('Failed to cancel booking.', 'bookinn')));
        }
    }
    
    /**
     * Create new booking
     */
    public function create_booking() {
        check_ajax_referer('bookinn_new_booking', 'booking_nonce');
        
        if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions.', 'bookinn'));
        }
        
        // Validate and sanitize input
        $booking_data = array(
            'guest' => array(
                'name' => sanitize_text_field($_POST['guest_name']),
                'email' => sanitize_email($_POST['guest_email']),
                'phone' => sanitize_text_field($_POST['guest_phone'])
            ),
            'room_id' => intval($_POST['room_id']),
            'check_in' => sanitize_text_field($_POST['check_in']),
            'check_out' => sanitize_text_field($_POST['check_out']),
            'adults' => intval($_POST['adults']),
            'children' => intval($_POST['children']),
            'notes' => sanitize_textarea_field($_POST['notes'])
        );
        
        // Validate required fields
        if (empty($booking_data['guest']['name']) || empty($booking_data['guest']['email']) || 
            empty($booking_data['room_id']) || empty($booking_data['check_in']) || empty($booking_data['check_out'])) {
            wp_send_json_error(array('message' => __('Please fill in all required fields.', 'bookinn')));
        }
        
        // Validate dates
        $check_in_date = DateTime::createFromFormat('Y-m-d', $booking_data['check_in']);
        $check_out_date = DateTime::createFromFormat('Y-m-d', $booking_data['check_out']);
        
        if (!$check_in_date || !$check_out_date || $check_in_date >= $check_out_date) {
            wp_send_json_error(array('message' => __('Invalid dates. Check-out must be after check-in.', 'bookinn')));
        }
        
        // Check room availability
        $available_rooms = BookInn_Models::get_available_rooms($booking_data['check_in'], $booking_data['check_out']);
        $room_available = false;
        
        foreach ($available_rooms as $room) {
            if ($room['id'] == $booking_data['room_id']) {
                $room_available = true;
                break;
            }
        }
        
        if (!$room_available) {
            wp_send_json_error(array('message' => __('Selected room is not available for the chosen dates.', 'bookinn')));
        }
        
        // Create booking
        $booking_id = BookInn_Models::create_booking($booking_data);
        
        if ($booking_id) {
            wp_send_json_success(array(
                'message' => __('Booking created successfully.', 'bookinn'),
                'booking_id' => $booking_id
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to create booking. Please try again.', 'bookinn')));
        }
    }
}

// Initialize the AJAX handler
new BookInn_Widget_Ajax_Handler();
